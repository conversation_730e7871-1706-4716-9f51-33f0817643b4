import { ShareParams, storage } from '@jgl/utils';
import {
  LearnPkgApiQueryLearnPkgClassifyDTO,
  LearnPkgClassifyDTOLearnPkgVersion,
  UsersApiUserInfoDTO,
} from '@yunti-private/api-xingdeng-boot';
import { ExpoUpdateConfig } from '@yunti-private/rn-expo-updates-helper';
import { atom } from 'jotai';
import { atomWithStorage } from 'jotai/utils';
import {
  ChooseOptionDTO,
  GroAdConf,
  IdentityDTO,
  RemoteUpdateConfDTO,
} from '../api/dto';
import { groMoreCatchAdKey } from '../constants';
import { AppVersionDTO, MarketDTO } from '../dtos/AppVersion';
import { DownloadStatus, ExpoUpdatesInfoOnLaunch } from '../types';
import { Platform } from 'react-native';
import { native } from '../utils/Native';
import { BaseInfo } from '../dtos/BaseInfo';
import { storageKeys } from '../utils/constants';
import { isEmpty } from 'lodash';
import { getUserInfo, store } from '@jgl/biz-func';

export const atomMap = {
  defaultClassifyAtom: atom<LearnPkgApiQueryLearnPkgClassifyDTO | undefined>(
    undefined,
  ),
  /**
   * 默认选择的学科的 Index
   */
  defaultSelectedSubjectIndexAtom: atom(0),

  /**
   * 默认选中的的学科的 名称
   */
  defaultSelectedSubjectTitleAtom: atom<string | undefined>(undefined),

  /**
   * 默认选择的版本
   */
  defaultSelectedVersionAtom: atom<
    LearnPkgClassifyDTOLearnPkgVersion | undefined
  >(undefined),

  /**
   * 用户个人资料
   */
  profileAtom: atom<UsersApiUserInfoDTO | undefined>(undefined),

  /**
   * 检查app版本
   */
  checkedAppVersionResultAtom: atom<{
    appVersionDTO?: AppVersionDTO;
    downloadStatus?: DownloadStatus;
    sysCheck?: boolean;
    market?: MarketDTO;
    isShowUpgradeModal: boolean;
  }>({ isShowUpgradeModal: false }),

  /**
   * 热更新配置
   */
  updateConfigAtom: atom<ExpoUpdateConfig | undefined>(undefined),
  /**
   * 远端热更新配置项
   */
  remoteUpdateConf: atom<RemoteUpdateConfDTO | undefined>(undefined),
  /**
   * 启动时热更新 SDK 返回信息
   */
  updateInfoOnLaunch: atom<ExpoUpdatesInfoOnLaunch | undefined>(undefined),

  /**
   * 是否是 学生
   */
  isStudentAtom: atom(false),

  /**
   * 热更新配置
   */
  updateConfigAtom: atom<ExpoUpdateConfig | undefined>(undefined),
  /**
   * 远端热更新配置项
   */
  remoteUpdateConf: atom<RemoteUpdateConfDTO | undefined>(undefined),

  /* 是否更新首页的vip信息 */
  isUpdateVipInIndex: atom(false),

  /* 是否更新详情的vip信息 */
  isUpdateVipInDetail: atom(false),

  isSentryInitializedAtom: atom(false),

  /**
   * 背包页面分享配置
   */
  backpackShareConfigs: atom<ShareParams | undefined>(undefined),

  /** 首页历史刷新 */
  needRefreshHistory: atom<boolean>(false),
};

/** beizi广告是否初始化 */
export const beiziAdInitSuccessAtom = atom<boolean>(false);

export const adConfAtom = atom<{
  /** 应用id */
  appid?: string;
  /** 开屏 */
  splashId?: string;
  /** 激励视频 */
  rewardedId?: string;
  rewardedInterval?: number;
  /** 全屏视频广告 */
  fullScreenId?: string;
  /** 横幅广告 */
  bannerId?: string;
  /** 原生广告 */
  nativeId?: string;
  /** 原生浮窗 */
  floatId?: string;
  /** 原生通知 */
  notificationId?: string;
  /** 插屏 */
  interstitialId?: string;
}>({});

/** 广告配置 */
// export const adConfAtom = atomWithStorage<{
//   /** 应用id */
//   appid?: string;
//   iosSplashId?: string;
//   /** 开屏 */
//   splashId?: string;
//   /** 激励视频 */
//   rewardedId?: string;
//   rewardedInterval?: number;
//   /** 全屏视频广告 */
//   fullScreenId?: string;
//   /** 横幅广告 */
//   bannerId?: string;
//   /** 原生广告 */
//   nativeId?: string;
//   /** 原生浮窗 */
//   floatId?: string;
//   /** 原生通知 */
//   notificationId?: string;
//   /** 插屏 */
//   interstitialId?: string;
// }>(groMoreCatchAdKey, testBeiZiAdIdConfig, {
//   setItem: async (key, newValue) => {
//     await storage.setItem(key, newValue);
//   },
//   getItem: async (key, initialValue) => {
//     try {
//       const result = await storage.getItem(key);
//       return JSON.parse(result ?? '{}');
//     } catch (error) {
//       return {};
//     }
//   },
//   removeItem: async (key) => {
//     await storage.removeItem(key);
//   },
// });

/** 身份选择信息 */
export const identityInfoAtom = atom<IdentityDTO>({});

/** 用户是否授权相机权限 */
export const cameraAuthStatusAtom = atom<boolean>(false);

/** 相机授权弹窗open状态 */
export const cameraAuthModalOpenAtom = atom<boolean>(false);

/** 年级数据 */
export const gradesAtom = atom<ChooseOptionDTO[]>([]);

/** 学科数据 */
export const subjectsAtom = atom<ChooseOptionDTO[]>([]);

/** 版本数据 */
export const versionsAtom = atom<ChooseOptionDTO[]>([]);

/** 基础信息 */
export const baseInfoAtom = atom(native.readBaseInfo());

/** 获取用户特定key */
const getUserAtomSpecificKey = (key: string) => {
  const userInfo = store.getState().userInfo;
  return `${key}_${userInfo.userId}`;
};

/** 每天书本查看数量 */
export const viewedBooksAtom = atomWithStorage<{
  date: string;
  bookIds: string[];
}>(
  getUserAtomSpecificKey(storageKeys.viewedBooks),
  { date: new Date().toDateString(), bookIds: [] },
  {
    setItem: async (key, newValue) => {
      await storage.setItem(key, newValue, { env: false });
    },
    getItem: async (key, initialValue) => {
      const result = await storage.getItem(key, { env: false });
      if (result) {
        return JSON.parse(result);
      }
      return initialValue ?? { date: new Date().toDateString(), bookIds: [] };
    },
    removeItem: async (key) => {
      await storage.removeItem(key, { env: false });
    },
  },
);

/** 应用渠道 */
export const appChannelAtom = atomWithStorage<string>(
  storageKeys.firstOpenInstallChannel,
  'loading',
  {
    setItem: async (key, newValue) => {
      await storage.setItem(key, newValue, { env: false });
    },
    getItem: async (key, initialValue) => {
      if (Platform.OS === 'ios') {
        return 'appStore';
      }
      let firstChannel;
      try {
        firstChannel = await storage.getItem(key, { env: false });
      } catch (error) {
        //
      }
      if (firstChannel && !isEmpty(firstChannel)) {
        // channel固定之后不会变
        return firstChannel;
      } else {
        const channel = await native.getCurrentAndroidChannel();
        return !channel || isEmpty(channel) ? 'other' : channel;
      }
    },
    removeItem: async (key) => {
      await storage.removeItem(key, { env: false });
    },
  },
);

/** 是否已经进行家长认证 */
export const isParentVerifiedAtom = atomWithStorage<boolean | undefined>(
  getUserAtomSpecificKey(storageKeys.isParentVerified),
  undefined,
  {
    setItem: async (key, newValue) => {
      await storage.setItem(key, String(newValue), { env: false });
    },
    getItem: async (key, initialValue) => {
      const result = await storage.getItem(key, { env: false });
      if (result == null) {
        // 默认家长模式开启，可以让用户查看答案
        return true;
      } else {
        return result == 'true';
      }
    },
    removeItem: async (key) => {
      await storage.removeItem(key, { env: false });
    },
  },
);

/** 是否开启家长认证 */
export const isOpenParentVerifiedAtom = atomWithStorage<
  boolean | undefined | null
>(getUserAtomSpecificKey(storageKeys.isOpenParentVerified), undefined, {
  setItem: async (key, newValue) => {
    await storage.setItem(key, String(newValue), { env: false });
  },
  getItem: async (key, initialValue) => {
    const result = await storage.getItem(key, { env: false });
    if (result == undefined) {
      return result;
    }
    return result == 'true';
  },
  removeItem: async (key) => {
    await storage.removeItem(key, { env: false });
  },
});

/** 用户书籍查看答案详情页面最后一次阅读页码 */
export const userBookAnswerLastPageIndexMapAtom = atomWithStorage<{
  [userId: number]: { [bookId: number]: number };
}>(
  storageKeys.userBookAnswerLastPageIndexMap,
  {},
  {
    setItem: async (key, newValue) => {
      await storage.setItem(key, JSON.stringify(newValue));
    },
    getItem: async (key, initialValue) => {
      const result = await storage.getItem(key);
      if (result) {
        return JSON.parse(result);
      }
      return initialValue ?? {};
    },
    removeItem: async (key) => {
      await storage.removeItem(key);
    },
  },
);
