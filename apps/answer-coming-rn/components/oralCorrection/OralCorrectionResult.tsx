import React, { useCallback, useEffect, useMemo } from 'react';
import {
  YTImage,
  YTText,
  YTTouchable,
  YTXStack,
  YTYStack,
  YTView,
  YTScrollView,
} from '@bookln/cross-platform-components';
import { useSafeAreaInsets } from '@jgl/biz-func';
import { OralCorrectionResult as ResultType } from '../../hooks/useOralCorrection';

interface OralCorrectionResultProps {
  visible: boolean;
  imageUrl?: string;
  checkDTO?: ResultType;
  loading?: boolean;
  onRetake?: () => void;
  onViewHistory?: () => void;
}

interface ResultInfo {
  coord: number[];
  correct: number;
  judgeResult: number;
  value: string;
  answer?: {
    value: string;
    type: string;
  };
}

export const OralCorrectionResult: React.FC<OralCorrectionResultProps> = ({
  visible,
  imageUrl,
  checkDTO,
  loading = false,
  onRetake,
  onViewHistory,
}) => {
  const { bottom } = useSafeAreaInsets();

  const [imageSize, setImageSize] = React.useState({ width: 0, height: 0 });

  useEffect(() => {
    if (imageUrl) {
      // 获取图片尺寸
      // 这里可以使用Image.getSize或其他方法获取图片尺寸
      setImageSize({ width: 300, height: 400 }); // 临时设置
    }
  }, [imageUrl]);

  const rightPercent = useMemo(() => {
    if (!checkDTO?.rightNums || !checkDTO?.errorNums) return 0;
    const total = checkDTO.rightNums + checkDTO.errorNums;
    return total > 0 ? (checkDTO.rightNums / total) * 100 : 0;
  }, [checkDTO?.rightNums, checkDTO?.errorNums]);

  const renderErrorMarks = useCallback(() => {
    if (!checkDTO?.resultInfos || !imageSize.width || !imageSize.height) {
      return null;
    }

    return checkDTO.resultInfos.map((result: any, index: number) => {
      const recResults = result.recResult || [];
      
      return recResults.map((recResult: ResultInfo, recIndex: number) => {
        if (recResult.judgeResult !== 0) return null; // 只显示错误的

        const [x1, y1, x2, y2] = recResult.coord;
        const left = (x1 * imageSize.width);
        const top = (y1 * imageSize.height);
        const width = ((x2 - x1) * imageSize.width);
        const height = ((y2 - y1) * imageSize.height);

        return (
          <YTTouchable
            key={`${index}-${recIndex}`}
            position="absolute"
            style={{
              left,
              top,
              width,
              height,
            }}
            onPress={() => {
              // 显示正确答案
              console.log('正确答案:', recResult.answer?.value);
            }}
          >
            <YTView
              w="100%"
              h="100%"
              borderWidth={2}
              borderColor="red"
              borderRadius={4}
              bg="rgba(255,0,0,0.1)"
              ai="center"
              jc="center"
            >
              <YTView
                w={20}
                h={20}
                borderRadius={10}
                bg="red"
                ai="center"
                jc="center"
              >
                <YTText color="white" fontSize={12} fontWeight="bold">
                  ✕
                </YTText>
              </YTView>
            </YTView>
          </YTTouchable>
        );
      });
    });
  }, [checkDTO?.resultInfos, imageSize]);

  const renderResultDescription = useMemo(() => {
    if (!checkDTO?.rightNums && !checkDTO?.errorNums) return null;

    const total = (checkDTO.rightNums || 0) + (checkDTO.errorNums || 0);

    return (
      <YTXStack ai="center" gap={4}>
        <YTText fontSize={18} color="#1f1f1f" fontWeight="600">
          共
        </YTText>
        <YTText fontSize={18} color="#52C41A" fontWeight="600">
          {total}
        </YTText>
        <YTText fontSize={18} color="#1f1f1f" fontWeight="600">
          题，正确率
        </YTText>
        <YTText fontSize={18} color="#52C41A" fontWeight="600">
          {rightPercent.toFixed(1)}%
        </YTText>
      </YTXStack>
    );
  }, [checkDTO?.rightNums, checkDTO?.errorNums, rightPercent]);

  const renderContent = () => {
    if (loading) {
      return (
        <YTYStack flex={1} ai="center" jc="center">
          <YTText fontSize={16} color="#666">
            正在批改中...
          </YTText>
        </YTYStack>
      );
    }

    if (checkDTO?.errorMsg) {
      return (
        <YTYStack flex={1} ai="center" jc="center" px={20}>
          <YTText fontSize={16} color="#ff4d4f" textAlign="center">
            {checkDTO.errorMsg}
          </YTText>
          {onRetake && (
            <YTTouchable
              mt={20}
              px={20}
              py={10}
              bg="#1890ff"
              borderRadius={8}
              onPress={onRetake}
            >
              <YTText color="white" fontSize={16}>
                重新拍照
              </YTText>
            </YTTouchable>
          )}
        </YTYStack>
      );
    }

    if (!checkDTO?.resultInfos || checkDTO.resultInfos.length === 0) {
      return (
        <YTYStack flex={1} ai="center" jc="center" px={20}>
          <YTText fontSize={16} color="#666" textAlign="center">
            暂未识别出题目，再拍一次吧~
          </YTText>
          {onRetake && (
            <YTTouchable
              mt={20}
              px={20}
              py={10}
              bg="#1890ff"
              borderRadius={8}
              onPress={onRetake}
            >
              <YTText color="white" fontSize={16}>
                重新拍照
              </YTText>
            </YTTouchable>
          )}
        </YTYStack>
      );
    }

    return (
      <YTScrollView flex={1} showsVerticalScrollIndicator={false}>
        <YTYStack flex={1}>
          {/* 图片和标记区域 */}
          <YTView position="relative" ai="center" py={20}>
            {imageUrl && (
              <YTImage
                source={{ uri: imageUrl }}
                style={{
                  width: imageSize.width,
                  height: imageSize.height,
                }}
                resizeMode="contain"
                onLoad={(event) => {
                  const { width, height } = event.nativeEvent.source;
                  setImageSize({ width: width * 0.8, height: height * 0.8 });
                }}
              />
            )}
            {renderErrorMarks()}
          </YTView>

          {/* 结果描述 */}
          <YTYStack ai="center" py={20}>
            {renderResultDescription}
            <YTXStack ai="center" mt={10} gap={4}>
              <YTText color="#4E5969" fontSize={14}>
                点击
              </YTText>
              <YTView
                w={20}
                h={20}
                borderRadius={10}
                bg="red"
                ai="center"
                jc="center"
              >
                <YTText color="white" fontSize={10}>
                  ✕
                </YTText>
              </YTView>
              <YTText color="#4E5969" fontSize={14}>
                标记的错误答案题，可查看正确答案
              </YTText>
            </YTXStack>
          </YTYStack>
        </YTYStack>
      </YTScrollView>
    );
  };

  if (!visible) return null;

  return (
    <YTYStack
      flex={1}
      bg="white"
      style={{ paddingBottom: bottom || 16 }}
    >
      {renderContent()}

      {/* 底部按钮 */}
      <YTXStack px={16} py={12} gap={12}>
        {onViewHistory && (
          <YTTouchable
            flex={1}
            py={12}
            bg="#f5f5f5"
            borderRadius={8}
            ai="center"
            onPress={onViewHistory}
          >
            <YTText color="#666" fontSize={16}>
              查看记录
            </YTText>
          </YTTouchable>
        )}
        {onRetake && (
          <YTTouchable
            flex={1}
            py={12}
            bg="#1890ff"
            borderRadius={8}
            ai="center"
            onPress={onRetake}
          >
            <YTText color="white" fontSize={16}>
              再拍一次
            </YTText>
          </YTTouchable>
        )}
      </YTXStack>
    </YTYStack>
  );
};
