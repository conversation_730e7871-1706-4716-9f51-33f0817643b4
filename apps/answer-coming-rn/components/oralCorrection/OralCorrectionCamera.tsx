import React, { useCallback, useRef, useState, useEffect } from 'react';
import { Platform } from 'react-native';
import ImageResizer from '@bam.tech/react-native-image-resizer';
import {
  YTImage,
  YTText,
  YTTouchable,
  YTXStack,
  YTYStack,
  YTView,
} from '@bookln/cross-platform-components';
import {
  PermissionEnum,
  PermissionHooks,
  PermissionPurposeScene,
} from '@bookln/permission';
import {
  useNavigationBarHeight,
  useSafeAreaInsets,
} from '@jgl/biz-func';
import { showToast, useDidHide, useDidShow } from '@jgl/utils';
import { useUnmount } from 'ahooks';
import { CameraView } from 'expo-camera';
import * as ImagePicker from 'expo-image-picker';

interface OralCorrectionCameraProps {
  onTakePhoto: (imageUri: string) => void;
  loading?: boolean;
}

export const OralCorrectionCamera: React.FC<OralCorrectionCameraProps> = ({
  onTakePhoto,
  loading = false,
}) => {
  const { bottom } = useSafeAreaInsets();
  const navigationBarHeight = useNavigationBarHeight();
  const { checkAndRequestPermission } = PermissionHooks.usePermission();

  const cameraRef = useRef<CameraView>(null);
  const [cameraActive, setCameraActive] = useState(true);
  const [enableTorch, setEnableTorch] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  useDidShow(() => {
    setCameraActive(true);
  });

  useDidHide(() => {
    setCameraActive(false);
  });

  useUnmount(() => {
    setCameraActive(false);
  });

  useEffect(() => {
    setIsUploading(loading);
  }, [loading]);

  const processImage = useCallback(async (uri: string) => {
    try {
      const resizedImage = await ImageResizer.createResizedImage(
        uri,
        1200,
        1600,
        'JPEG',
        80,
        0,
        undefined,
        false,
        {
          mode: 'contain',
          onlyScaleDown: true,
        }
      );
      return resizedImage.uri;
    } catch (error) {
      console.error('图片处理失败:', error);
      return uri;
    }
  }, []);

  const onCommitPhoto = useCallback(
    async (photoInfo: { url: string; width: number; height: number; size: number }) => {
      if (isUploading) return;

      try {
        setIsUploading(true);
        const processedUri = await processImage(photoInfo.url);
        onTakePhoto(processedUri);
      } catch (error) {
        console.error('OralCorrectionCamera - onCommitPhoto - error', error);
        setIsUploading(false);
        showToast({
          title: '图片处理失败',
        });
      }
    },
    [isUploading, processImage, onTakePhoto]
  );

  const onPressTakePhoto = useCallback(async () => {
    if (cameraRef.current && !isUploading) {
      try {
        const result = await cameraRef.current.takePictureAsync({
          base64: false,
          quality: 1,
        });

        if (result) {
          onCommitPhoto({
            url: result.uri,
            width: result.width,
            height: result.height,
            size: 0,
          });
        }
      } catch (error) {
        console.error('拍照失败:', error);
        showToast({
          title: '拍照失败，请重试',
        });
      }
    }
  }, [isUploading, onCommitPhoto]);

  const onPressOpenAlbum = useCallback(async () => {
    if (isUploading) return;

    const authResult = await checkAndRequestPermission({
      permission: PermissionEnum.Album,
      scene: PermissionPurposeScene.ChoicePicture,
    });

    if (authResult) {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        aspect: [4, 3],
        quality: 1,
        selectionLimit: 1,
      });

      if (!result.canceled && result.assets.length > 0) {
        const asset = result.assets[0];
        if (asset) {
          const { uri, width, height } = asset;
          onCommitPhoto({
            url: uri,
            width,
            height,
            size: 0,
          });
        }
      }
    }
  }, [isUploading, checkAndRequestPermission, onCommitPhoto]);

  const onPressToggleTorch = useCallback(() => {
    setEnableTorch(!enableTorch);
  }, [enableTorch]);

  return (
    <YTYStack flex={1} bg="black">
      {/* 相机预览区域 */}
      <YTYStack flex={1} position="relative">
        {Platform.OS === 'ios' ? (
          <CameraView
            active={cameraActive}
            style={{
              width: '100%',
              height: '100%',
              backgroundColor: 'black',
            }}
            ref={cameraRef}
            facing="back"
            enableTorch={enableTorch}
            onMountError={(e) => {
              console.error('相机初始化失败:', e.message);
            }}
          />
        ) : cameraActive ? (
          <CameraView
            active={cameraActive}
            style={{
              width: '100%',
              height: '100%',
              backgroundColor: 'black',
            }}
            ref={cameraRef}
            facing="back"
            enableTorch={enableTorch}
            onMountError={(e) => {
              console.error('相机初始化失败:', e.message);
            }}
          />
        ) : null}

        {/* 拍照提示 */}
        <YTYStack
          position="absolute"
          top={navigationBarHeight + 20}
          left={0}
          right={0}
          ai="center"
        >
          <YTYStack
            bg="rgba(0,0,0,0.6)"
            px={16}
            py={8}
            borderRadius={20}
            ai="center"
          >
            <YTText color="white" fontSize={16} fontWeight="bold">
              一次拍摄一整页
            </YTText>
            <YTText color="white" fontSize={14} mt={4}>
              题目与参考线平行
            </YTText>
          </YTYStack>
        </YTYStack>

        {/* 参考线 */}
        <YTView
          position="absolute"
          top="50%"
          left={40}
          right={40}
          height={2}
          bg="rgba(255,255,255,0.8)"
          style={{ transform: [{ translateY: -1 }] }}
        />
      </YTYStack>

      {/* 底部操作区域 */}
      <YTYStack
        bg="black"
        style={{ paddingBottom: bottom + 12, paddingTop: 12 }}
      >
        <YTXStack h={98} ai="center" jc="space-between" px={20}>
          {/* 相册按钮 */}
          <YTTouchable
            w={60}
            h={60}
            ai="center"
            jc="center"
            onPress={onPressOpenAlbum}
            disabled={isUploading}
          >
            <YTYStack
              w={40}
              h={40}
              bg="rgba(255,255,255,0.3)"
              borderRadius={8}
              ai="center"
              jc="center"
            >
              <YTText color="white" fontSize={12}>
                相册
              </YTText>
            </YTYStack>
          </YTTouchable>

          {/* 拍照按钮 */}
          <YTTouchable
            w={80}
            h={80}
            ai="center"
            jc="center"
            onPress={onPressTakePhoto}
            disabled={isUploading}
          >
            <YTView
              w={70}
              h={70}
              borderRadius={35}
              bg="white"
              ai="center"
              jc="center"
            >
              {isUploading ? (
                <YTText fontSize={12} color="black">
                  处理中
                </YTText>
              ) : (
                <YTView
                  w={60}
                  h={60}
                  borderRadius={30}
                  bg="white"
                  borderWidth={3}
                  borderColor="black"
                />
              )}
            </YTView>
          </YTTouchable>

          {/* 闪光灯按钮 */}
          <YTTouchable
            w={60}
            h={60}
            ai="center"
            jc="center"
            onPress={onPressToggleTorch}
            disabled={isUploading}
          >
            <YTYStack
              w={40}
              h={40}
              bg={enableTorch ? "rgba(255,255,0,0.3)" : "rgba(255,255,255,0.3)"}
              borderRadius={8}
              ai="center"
              jc="center"
            >
              <YTText color="white" fontSize={12}>
                {enableTorch ? '关闪' : '开闪'}
              </YTText>
            </YTYStack>
          </YTTouchable>
        </YTXStack>
      </YTYStack>
    </YTYStack>
  );
};
