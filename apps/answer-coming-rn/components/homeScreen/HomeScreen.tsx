import {
  Y<PERSON><PERSON><PERSON>,
  Y<PERSON><PERSON>tActionType,
  YTAlertGlobal,
  YTGridView,
  YTImage,
  YTScrollView,
  type YTScrollViewRef,
  YTSpinner,
  YTStateView,
  YTText,
  YTTouchable,
  YTView,
  YTXStack,
  YTYStack,
} from '@bookln/cross-platform-components';
import { Search } from '@bookln/icon-lucide';
import {
  PermissionEnum,
  PermissionHooks,
  PermissionPurposeScene,
} from '@bookln/permission';
import { router } from '@jgl/utils';
import { LinearGradient } from 'expo-linear-gradient';
import { useFocusEffect } from 'expo-router';
import { useCallback, useMemo, useRef } from 'react';
import {
  type NativeScrollEvent,
  type NativeSyntheticEvent,
  RefreshControl,
  Text,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from 'tamagui';
import { type BookDTO, SeekAssistStatus } from '../../api/dto';
import { withAgreementCheck } from '../../utils/agreementHelper';
import { AuditStatus } from '../AuditStatusTag';
import { useHistoryAnswerBooks } from './useHistoryAnswerBooks';

export const HomeScreen = () => {
  const isFirstLoadRef = useRef(true);

  const theme = useTheme();

  const scrollRef = useRef<YTScrollViewRef>(null);

  const { checkAndRequestPermission } = PermissionHooks.usePermission();

  const {
    historyAnswerBooks,
    initialLoading,
    fetchFirstPage,
    fetchNextPage,
    handleDelete,
    hasMore,
    loading,
    isRefresh,
  } = useHistoryAnswerBooks({ scrollRef });

  useFocusEffect(
    useCallback(() => {
      if (isFirstLoadRef.current) {
        isFirstLoadRef.current = false;
      } else {
        fetchFirstPage({ noLoadingIndicator: true });
      }
    }, [fetchFirstPage]),
  );

  const onPressToSearch = useCallback(() => {
    router.push('/result');
  }, []);

  const handleCameraPermission = useCallback(
    async (param: { navigateTo: string; scene: PermissionPurposeScene }) => {
      const { navigateTo, scene } = param;

      const granted = await checkAndRequestPermission({
        permission: PermissionEnum.Camera,
        scene,
      });
      if (granted) {
        router.push(navigateTo);
      }
    },
    [checkAndRequestPermission],
  );

  const onPressToBarScan = useCallback(() => {
    handleCameraPermission({
      navigateTo: '/barcodeScan',
      scene: PermissionPurposeScene.ScanBarCode,
    });
  }, [handleCameraPermission]);

  const onPressToQrScan = useCallback(() => {
    handleCameraPermission({
      navigateTo: '/qrcodeScan',
      scene: PermissionPurposeScene.ScanQRCode,
    });
  }, [handleCameraPermission]);

  const onPressToTakePhotoAndAnswer = useCallback(() => {
    handleCameraPermission({
      navigateTo: '/aiExplain',
      scene: PermissionPurposeScene.TakePhotoAiExplain,
    });
  }, [handleCameraPermission]);

  const onPressToTakePhotoAndTranslate = useCallback(() => {
    handleCameraPermission({
      navigateTo: '/(takePhotoTranslation)/takePhotoTranslationCamera',
      scene: PermissionPurposeScene.TakePhotoTranslationCamera,
    });
  }, [handleCameraPermission]);

  const onPressToTakePhotoOralCorrection = useCallback(() => {
    handleCameraPermission({
      navigateTo: '/oralCorrection',
      scene: PermissionPurposeScene.TakePhotoAiExplain,
    });
  }, [handleCameraPermission]);

  const onPressToDetail = useCallback((item: BookDTO) => {
    router.push(`/book?id=${item.id}`);
  }, []);

  const isCloseToBottom = useCallback(
    (nativeEvent: NativeSyntheticEvent<NativeScrollEvent>) => {
      const { layoutMeasurement, contentOffset, contentSize } =
        nativeEvent.nativeEvent;
      const paddingToBottom = 20;
      return (
        layoutMeasurement.height + contentOffset.y >=
        contentSize.height - paddingToBottom
      );
    },
    [],
  );

  const onScroll = useCallback(
    (nativeEvent: NativeSyntheticEvent<NativeScrollEvent>) => {
      if (hasMore && !loading) {
        if (isCloseToBottom(nativeEvent)) {
          fetchNextPage();
        }
      }
    },
    [fetchNextPage, hasMore, isCloseToBottom, loading],
  );

  // 背景渐变颜色
  // 图片资源
  const imageResources = useMemo(
    () => ({
      homeBgPoints: require('../../assets/images/home_bg_points.png'),
      homeBgWave: require('../../assets/images/home_bg_wave.png'),
      iconScanBarCode: require('../../assets/images/icon_scan_bar_code.png'),
      iconScanQrCode: require('../../assets/images/icon_scan_qr_code.png'),
      iconTakePhotoAndAnswer: require('../../assets/images/icon_take_photo_and_answer.png'),
      iconTakePhotoAndTranslate: require('../../assets/images/icon_take_photo_and_translate.png'),
      iconTakePhotoOralCorrection:
        'https://ytpan.bookln.cn/btpan/secure/stand/product/organize/4/23/195949098_20250317105653_vrfhm.png',
    }),
    [],
  );

  // 文本样式
  const titleTextStyle = useMemo(
    () => ({
      fontSize: 12,
      color: theme.gray12.val,
      fontWeight: '600' as const,
      fontFamily: 'Alimama FangYuanTi VF',
    }),
    [theme.gray12.val],
  );

  const mainTitleTextStyle = useMemo(
    () => ({
      fontSize: 18,
      color: theme.gray12.val,
      fontWeight: '600' as const,
      fontFamily: 'Alimama FangYuanTi VF',
    }),
    [theme.gray12.val],
  );

  // 背景组件
  const BackgroundSection = useMemo(
    () => (
      <YTYStack w={'$full'} position='absolute' h={183} bg={'red'}>
        <LinearGradient
          className='relative h-full'
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          colors={['#FFE855', '#FFFF83']}
        >
          <YTImage
            source={imageResources.homeBgPoints}
            w={'$full'}
            h={'$full'}
          />
          <YTImage
            source={imageResources.homeBgWave}
            w={122}
            h={90}
            position='absolute'
            top={0}
            right={'20%'}
          />
        </LinearGradient>
      </YTYStack>
    ),
    [imageResources.homeBgPoints, imageResources.homeBgWave],
  );

  // 标题组件
  const TitleSection = useMemo(
    () => (
      <YTYStack bg={'transparent'} px={19} py={3}>
        <YTText style={titleTextStyle}>作业一扫</YTText>
        <YTText style={mainTitleTextStyle}>答案来了</YTText>
      </YTYStack>
    ),
    [titleTextStyle, mainTitleTextStyle],
  );

  // 搜索框组件
  const SearchSection = useMemo(
    () => (
      <YTYStack mx={16} my={8} px={12} py={8} bg={'white'} borderRadius={100}>
        <YTTouchable onPress={withAgreementCheck(onPressToSearch)}>
          <YTXStack gap={8} flex={1} ai='center' backgroundColor={'white'}>
            <Search width={24} height={24} color={theme.gray9.val} />
            <YTText fontSize={14} color={'$gray9'}>
              输入书名/条形码找答案
            </YTText>
          </YTXStack>
        </YTTouchable>
      </YTYStack>
    ),
    [onPressToSearch, theme.gray9.val],
  );

  // 扫码按钮组件
  const ScanButtonsSection = useMemo(
    () => (
      <YTXStack px={12} h={96}>
        <YTTouchable
          h={'$full'}
          flex={1}
          borderRadius={16}
          overflow='hidden'
          onPress={withAgreementCheck(onPressToBarScan)}
        >
          <LinearGradient
            style={{ height: '100%', flex: 1 }}
            start={{ x: 0, y: 0 }}
            end={{ x: 0, y: 1 }}
            colors={['#3075F9', '#5CB0F7']}
          >
            <YTXStack
              bg={'transparent'}
              px={18}
              h={'$full'}
              jc={'space-between'}
              ai='center'
            >
              <YTYStack jc={'center'} bg={'transparent'} flex={1}>
                <YTText color={'$background'} fontSize={18} fontWeight='bold'>
                  扫条形码
                </YTText>
                <YTText color={'$background'} fontSize={14}>
                  看答案
                </YTText>
              </YTYStack>
              <YTImage w={40} h={40} source={imageResources.iconScanBarCode} />
            </YTXStack>
          </LinearGradient>
        </YTTouchable>
        <YTYStack w={12} />
        <YTTouchable
          h={'$full'}
          flex={1}
          borderRadius={16}
          overflow='hidden'
          onPress={withAgreementCheck(onPressToQrScan)}
        >
          <LinearGradient
            style={{ height: '100%', flex: 1 }}
            start={{ x: 0, y: 0 }}
            end={{ x: 0, y: 1 }}
            colors={['#FF4D17', '#FFB6A0']}
          >
            <YTXStack
              bg={'transparent'}
              px={18}
              h={'$full'}
              jc={'space-between'}
              ai='center'
            >
              <YTYStack jc={'center'} bg={'transparent'} flex={1}>
                <YTText color={'$background'} fontSize={18} fontWeight='bold'>
                  扫二维码
                </YTText>
                <YTText color={'$background'} fontSize={14}>
                  看答案
                </YTText>
              </YTYStack>
              <YTImage w={40} h={40} source={imageResources.iconScanQrCode} />
            </YTXStack>
          </LinearGradient>
        </YTTouchable>
        <YTYStack />
      </YTXStack>
    ),
    [
      onPressToBarScan,
      imageResources.iconScanBarCode,
      imageResources.iconScanQrCode,
      onPressToQrScan,
    ],
  );

  // 功能按钮组件
  const FeatureButtonsSection = useMemo(
    () => (
      <YTXStack px={12} gap={12}>
        <YTYStack flex={1} ai={'center'} jc={'center'}>
          <YTTouchable
            flexDirection='column'
            display='flex'
            onPress={withAgreementCheck(onPressToTakePhotoAndAnswer)}
          >
            <YTImage
              source={imageResources.iconTakePhotoAndAnswer}
              w={40}
              h={40}
              gap={2}
            />
            <YTText>拍照答疑</YTText>
          </YTTouchable>
        </YTYStack>

        <YTYStack flex={1} ai={'center'} jc={'center'}>
          <YTTouchable
            flexDirection='column'
            display='flex'
            gap={2}
            onPress={withAgreementCheck(onPressToTakePhotoOralCorrection)}
          >
            <YTImage
              source={imageResources.iconTakePhotoOralCorrection}
              w={40}
              h={40}
            />
            <YTText>口算批改</YTText>
          </YTTouchable>
        </YTYStack>

        <YTYStack flex={1} ai={'center'} jc={'center'}>
          <YTTouchable
            flexDirection='column'
            display='flex'
            gap={2}
            onPress={withAgreementCheck(onPressToTakePhotoAndTranslate)}
          >
            <YTImage
              source={imageResources.iconTakePhotoAndTranslate}
              w={40}
              h={40}
            />
            <YTText>拍照翻译</YTText>
          </YTTouchable>
        </YTYStack>
      </YTXStack>
    ),
    [
      imageResources.iconTakePhotoAndAnswer,
      imageResources.iconTakePhotoAndTranslate,
      imageResources.iconTakePhotoOralCorrection,
      onPressToTakePhotoAndAnswer,
      onPressToTakePhotoAndTranslate,
      onPressToTakePhotoOralCorrection,
    ],
  );

  // 答案册标题组件
  const AnswerBookTitleSection = useMemo(
    () => (
      <YTXStack px={16} py={10}>
        <YTText fontSize={16} fontWeight='bold'>
          答案册
        </YTText>
      </YTXStack>
    ),
    [],
  );

  const renderBottom = useMemo(() => {
    if (hasMore) {
      return <YTSpinner />;
    }
    if (!hasMore) {
      return (
        <YTXStack py={24} ai={'center'} jc={'center'} gap={4}>
          <YTView w={24} h={2} bg={'$color4'} borderRadius={2} />
          <YTView w={2} h={2} bg={'$color4'} borderRadius={2} />
          <YTView w={24} h={2} bg={'$color4'} borderRadius={2} />
        </YTXStack>
      );
    }
  }, [hasMore]);

  return (
    <YTYStack w={'$full'} h={'$full'} overflow='hidden' position='relative'>
      {BackgroundSection}
      <SafeAreaView edges={['top']}>
        <YTYStack w={'$full'} h={'$full'} overflow='hidden' bg={'transparent'}>
          {TitleSection}
          {SearchSection}
          <YTYStack
            w={'$full'}
            flex={1}
            overflow='hidden'
            borderTopLeftRadius={20}
            borderTopRightRadius={20}
          >
            <YTStateView isLoading={initialLoading} onRetry={fetchFirstPage}>
              <YTScrollView
                ref={scrollRef}
                refreshControl={
                  <RefreshControl
                    refreshing={isRefresh}
                    onRefresh={fetchFirstPage}
                    enabled
                  />
                }
                w={'$full'}
                h={'$full'}
                stickyHeaderIndices={[1]}
                onScroll={onScroll}
                scrollEventThrottle={400}
              >
                <YTYStack pt={12} pb={8} gap={8}>
                  {ScanButtonsSection}
                  {FeatureButtonsSection}
                </YTYStack>
                {AnswerBookTitleSection}
                <YTGridView
                  px={16}
                  minColumns={3}
                  minItemWidth={100}
                  horizontalSpace={32}
                  gap={16}
                  style={{ alignSelf: 'center' }}
                >
                  {historyAnswerBooks?.map((item) => {
                    const renderTag = () => {
                      if (!item.hasAnswer) {
                        if (item.answerAuditStatus === AuditStatus.Wait) {
                          return (
                            <YTView
                              position='absolute'
                              top={0}
                              right={0}
                              borderTopRightRadius={8}
                              borderBottomLeftRadius={8}
                              overflow='hidden'
                            >
                              <LinearGradient
                                start={{ x: 0, y: 1 }}
                                end={{ x: 1, y: 1 }}
                                colors={['#FF5D18', '#FFB218']}
                              >
                                <YTYStack px={6} py={2} bg={'transparent'}>
                                  <Text
                                    style={{
                                      fontSize: 12,
                                      fontFamily: 'DingTalk JinBuTi',
                                      color: 'white',
                                    }}
                                  >
                                    答案审核中
                                  </Text>
                                </YTYStack>
                              </LinearGradient>
                            </YTView>
                          );
                        }
                        if (item.seekAssistStatus === SeekAssistStatus.Pass) {
                          return (
                            <YTView
                              position='absolute'
                              top={0}
                              right={0}
                              borderTopRightRadius={8}
                              borderBottomLeftRadius={8}
                              overflow='hidden'
                            >
                              <LinearGradient
                                start={{ x: 0, y: 1 }}
                                end={{ x: 1, y: 1 }}
                                colors={['#FF2718', '#FF7C18']}
                              >
                                <YTYStack px={6} py={2} bg={'transparent'}>
                                  <Text
                                    style={{
                                      fontSize: 12,
                                      fontFamily: 'DingTalk JinBuTi',
                                      color: 'white',
                                    }}
                                  >
                                    {item?.seekAssistCount || 1}人求助中
                                  </Text>
                                </YTYStack>
                              </LinearGradient>
                            </YTView>
                          );
                        }
                      }
                      return null;
                    };

                    return (
                      <YTTouchable
                        key={item.id}
                        w={'$full'}
                        display='flex'
                        flexDirection='column'
                        onPress={() => {
                          onPressToDetail(item);
                        }}
                        onLongPress={() => {
                          YTAlert.show({
                            title: '确定删除此图书吗？',
                            actions: [
                              {
                                label: '取消',
                                type: YTAlertActionType.默认,
                                onPress: (hide) => {
                                  hide();
                                },
                              },
                              {
                                label: '删除',
                                type: YTAlertActionType.危险按扭,
                                onPress: async (hide) => {
                                  const success = await handleDelete(item);
                                  if (success) {
                                    hide();
                                  }
                                },
                              },
                            ],
                          });
                        }}
                      >
                        <YTView position='relative'>
                          <YTImage
                            source={{
                              uri: item.thumbnails,
                            }}
                            w={'$full'}
                            aspectRatio={104 / 150}
                            borderRadius={8}
                          />
                          {renderTag()}
                        </YTView>
                        <YTText fontSize={12} numberOfLines={2}>
                          {item.name}
                        </YTText>
                      </YTTouchable>
                    );
                  })}
                </YTGridView>
                {renderBottom}
              </YTScrollView>
            </YTStateView>
          </YTYStack>
        </YTYStack>
      </SafeAreaView>
      <YTAlertGlobal />
    </YTYStack>
  );
};
