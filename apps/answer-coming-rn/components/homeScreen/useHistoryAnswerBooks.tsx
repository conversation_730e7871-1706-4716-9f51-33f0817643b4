import { YTScrollViewRef } from '@bookln/cross-platform-components';
import { useAppSelector } from '@jgl/biz-func';
import { container } from '@jgl/container';
import { showToast } from '@jgl/utils';
import { useAtom } from 'jotai';
import { RefObject, useCallback, useEffect, useState } from 'react';
import { listSearchHistory, removeMyBook } from '../../api/api';
import { BookDTO } from '../../api/dto';
import { atomMap } from '../../atom';

export const useHistoryAnswerBooks = ({
  scrollRef,
}: {
  scrollRef: RefObject<YTScrollViewRef | null>;
}) => {
  const [needRefreshHistory, setNeedRefreshHistory] = useAtom(
    atomMap.needRefreshHistory,
  );

  const userInfo = useAppSelector((s) => s.userInfo);

  const [historyAnswerBooks, setHistoryAnswerBooks] = useState<BookDTO[]>([]);

  const [initialLoading, setInitialLoading] = useState(true);

  const [isRefresh, setIsRefreshing] = useState(false);

  const [loading, setLoading] = useState<boolean>(false);

  const [hasMore, setHasMore] = useState<boolean>(false);

  const [currentPage, setCurrentPage] = useState<number>(1);

  const fetchHistoryBooks = useCallback(async (nextPage: number) => {
    const res = await container.net().fetch(
      listSearchHistory({
        currentPage: nextPage,
        pageSize: 20,
      }),
    );
    if (res.success) {
      setCurrentPage(nextPage);
      setHasMore(
        !!res.data?.currentPage &&
          !!res.data?.totalPages &&
          res.data?.currentPage < res.data?.totalPages,
      );
      if (nextPage !== 1) {
        setHistoryAnswerBooks((prev) => [
          ...prev,
          ...(res.data?.pageData || []),
        ]);
      } else {
        setHistoryAnswerBooks(res.data?.pageData || []);
      }
    }
  }, []);

  const fetchFirstPage = useCallback(
    async (args?: { noLoadingIndicator?: boolean }) => {
      if (args?.noLoadingIndicator) {
        // 首页进入图书详情后返回，刷新时不需要展示loading、refreshing
      } else {
        if (!historyAnswerBooks.length) {
          setInitialLoading(true);
        } else {
          setIsRefreshing(true);
        }
      }

      await fetchHistoryBooks(1).finally(() => {
        scrollRef?.current?.scrollTo({ y: 0 });

        if (args?.noLoadingIndicator) {
          // 首页进入图书详情后返回，刷新时不需要展示loading、refreshing
        } else {
          if (!historyAnswerBooks.length) {
            setInitialLoading(false);
          } else {
            setIsRefreshing(false);
          }
        }
      });
    },
    [fetchHistoryBooks, historyAnswerBooks.length, scrollRef],
  );

  const fetchNextPage = useCallback(async () => {
    if (hasMore) {
      setLoading(true);
      await fetchHistoryBooks(currentPage + 1).finally(() => {
        setLoading(false);
      });
    }
  }, [currentPage, fetchHistoryBooks, hasMore]);

  useEffect(() => {
    fetchFirstPage();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userInfo.userId]);

  useEffect(() => {
    if (needRefreshHistory) {
      setNeedRefreshHistory(false);
      fetchFirstPage();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [needRefreshHistory]);

  const handleDelete = useCallback(
    async (item: BookDTO) => {
      if (!item.id) {
        return;
      }

      try {
        const res = await container
          .net()
          .fetch(removeMyBook({ bookId: item.id }));

        if (res.success) {
          showToast({ title: '删除成功' });
          fetchFirstPage();
          return true;
        } else {
          return false;
        }
      } catch (error) {
        return false;
      }
    },
    [fetchFirstPage],
  );

  return {
    historyAnswerBooks,
    initialLoading,
    loading,
    hasMore,
    isRefresh,
    fetchFirstPage,
    fetchNextPage,
    handleDelete,
  };
};
