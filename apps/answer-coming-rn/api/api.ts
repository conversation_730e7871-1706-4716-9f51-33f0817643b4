import {
  isPlatform,
  ListDTOWithPagination,
  PaginationParams,
} from '@jgl/biz-func';
import { FileUploadInfo } from '@jgl/upload/api/commServiceDTO';
import {
  UsersApiBindPhoneParams,
  UsersApiUnregisterDTO,
} from '@yunti-private/api-xingdeng-boot';
import { YTRequest } from '@yunti-private/net';
import {
  AccountInfoDTO,
  AnswerAuditRecordDTO,
  AnswersSubmitParams,
  BookDTO,
  BookHasCameraCutTaskDTO,
  ChooseOptionDTO,
  FetchBookParams,
  FetchHistoryParams,
  ListeningOriginalAudioDTO,
  RecordSearchHistoryParams,
  SaasBookInfoDTO,
  UserInfo,
} from './dto';

/** 图片上传接口 */
export const getFileUploadInfo = (): YTRequest<FileUploadInfo> => {
  return {
    url: '/dall/commservice/getFileUploadInfo.do',
    data: {
      bizCode: 'ac',
      storageTimeType: 1,
      now: Date.now(),
    },
  };
};

/** 上传15天有效 */
export const get15DayFileUploadInfoFor = (): YTRequest<FileUploadInfo> => {
  return {
    url: '/dall/commservice/get15DayFileUploadInfoFor.do',
    data: {
      bizCode: 'ac',
      storageTimeType: 1,
      now: Date.now(),
    },
  };
};

/** app登录 */
export const appLogin = (data: any): YTRequest<UserInfo> => {
  return {
    url: '/dallUser/appLogin.do',
    data,
  };
};

/** app退出登录 */
export const logout = (): YTRequest<boolean> => {
  return {
    url: '/dallUser/logout.do',
  };
};

/** 注销账号 */
export const usersApiUnregister = (): YTRequest<UsersApiUnregisterDTO> => {
  return {
    url: '/dallUser/unregister.do',
  };
};

/** 绑定手机号 */
export const bindPhone = (
  data: UsersApiBindPhoneParams,
): YTRequest<boolean> => {
  return {
    url: '/dallUser/bindPhone.do',
    data,
  };
};

/** 获取登录信息 */
export const getLoginInfo = (
  data: Record<string, string | number>,
): YTRequest<UserInfo> => {
  return {
    url: '/dallUser/getLoginInfo.do',
    data,
  };
};

/** 登录 */
export const wxMinAppLogin = (data: {
  unionId?: string;
  code?: string;
}): YTRequest<UserInfo> => {
  return {
    url: '/dallUser/wxminapplogin.do',
    data,
  };
};

/** 报错用户身份、年级信息 */
export const initUserInfo = (data: {
  type?: number;
  gradeCode?: string;
}): YTRequest<boolean> => {
  return {
    url: '/dallUser/initUserInfo.do',
    data,
  };
};

/** 获取学科年级版本 */
export const listByPCode = (data: {
  pCode: string;
  isInitUserInfo?: boolean;
}): YTRequest<ChooseOptionDTO[]> => {
  return {
    url: '/dallCat/listByPCode.do',
    data,
  };
};

/** 获取标准学科 */
export const getStandardSubjectCats = (): YTRequest<ChooseOptionDTO[]> => {
  return {
    url: '/dallCat/getStandardSubjectCats.do',
  };
};

/** 获取标准版本 */
export const getStandardVersionCats = (): YTRequest<ChooseOptionDTO[]> => {
  return {
    url: '/dallCat/getStandardVersionCats.do',
  };
};

/** 查询推荐答案 */
export const listRecommendedBook = (): YTRequest<BookDTO[]> => {
  return {
    url: '/dallBook/listRecommendedBook.do',
  };
};

/** 查询用户历史记录 */
export const listSearchHistory = (
  data: FetchHistoryParams,
): YTRequest<ListDTOWithPagination<BookDTO>> => {
  return {
    url: '/dallBook/listSearchHistory.do',
    data,
  };
};

export const removeMyBook = (data: { bookId: number }): YTRequest<any> => {
  return {
    url: '/dallBook/removeMyBook.do',
    data,
  };
};

/** 拍答案提交审核 */
export const answersSubmit = (
  params: AnswersSubmitParams,
): YTRequest<boolean> => {
  return {
    url: '/userUploadAnswerRecord/submit.do',
    data: params,
  };
};

/** 答案上传审核记录 */
export const answersAuditListPage = (
  data: PaginationParams,
): YTRequest<ListDTOWithPagination<AnswerAuditRecordDTO>> => {
  return {
    url: '/userUploadAnswerRecord/listPage.do',
    data,
  };
};

/** 获取上传记录详情 */
export const answersAuditGetById = (data: {
  id: number;
}): YTRequest<AnswerAuditRecordDTO> => {
  return {
    url: '/userUploadAnswerRecord/getById.do',
    data,
  };
};

/** 根据书名/isbn查询结果 */
export const listByIsbnOrName = (
  data: FetchBookParams,
): YTRequest<ListDTOWithPagination<BookDTO>> => {
  return {
    url: '/dallBook/listByIsbnOrName.do',
    data,
  };
};

/** 保存答案访问信息 */
export const recordSearchHistory = (
  data: RecordSearchHistoryParams,
): YTRequest<boolean> => {
  return {
    url: '/dallBook/recordSearchHistory.do',
    data,
  };
};

/** 获取图书信息 */
export const getById = (data: { id: number }): YTRequest<BookDTO> => {
  return {
    url: '/dallBook/getById.do',
    data,
  };
};

/** 获取配置项 */
export const getConfValByCode = (data: {
  confCode: string;
}): YTRequest<string> => {
  return {
    data,
    url: '/comm/getConfValByCode.do',
  };
};

/** 切题 */
export const cutQuestion = (params: { imageUrl: string }): YTRequest<any> => {
  return {
    url: '/dallBook/cutQuestion.do',
    data: params,
  };
};

/** 根据photoFeatureType调用AI(阿里模型) */
export const callAIByPhotoFeatureType = (params: {
  deviceId: string;
  url: string;
}): YTRequest<number> => {
  return {
    url: '/dallBook/callAIGetQuestionResult.do',
    data: params,
  };
};

/** 获取我的账户信息 */
export const getMyAccountInfo = (): YTRequest<AccountInfoDTO> => {
  return {
    url: '/dallUserAccount/myAccountInfo.do',
  };
};

/** 提现申请 */
export const submitWithdraw = (): YTRequest<boolean> => {
  return {
    url: '/dallUserAccount/submitWithdraw.do',
  };
};

/** 家长模式校验 */
export const idCardVerification = (data: {
  realName: string;
  idCard: string;
}): YTRequest<{ result: string }> => {
  return {
    url: '/dallUser/idCardVerification.do',
    data,
  };
};

/** 获取图书关联关系 */
export const querySaasBookIdV2 = (data: {
  url?: string;
}): YTRequest<SaasBookInfoDTO> => {
  return {
    url: '/dallBook/querySaasBookIdByUrlV2.do',
    data,
  };
};

/**获取书链关联关系 */
export const dallBookHasCameraCutTask = (data: {
  bookId: number;
}): YTRequest<BookHasCameraCutTaskDTO> => {
  return {
    url: '/dallBook/hasCameraCutTask.do',
    data,
  };
};

/** 获取听力原文音频列表 */
export const listSaasBookAudios = (data: {
  bookId: number | string;
}): YTRequest<ListeningOriginalAudioDTO[]> => {
  return {
    url: '/dallBook/listSaasBookAudios.do',
    data,
    project: isPlatform({ runtime: 'h5' }) ? 'h5mp' : 'booklnboot',
  };
};

/** 口算检查 */
export const cameraCorrect = (data: {
  url: string;
}): YTRequest<{ recordId: string }> => {
  const params = {
    /**
     * 业务类型
     */
    bizType: 2,
    bookId: 528417, // 生产环境bookId
    /**
     * 格式必须是数组，整页识别坐标
     */
    coord: '[0, 0, 1, 1]',
  };
  return {
    url: '/dallBook/cameraCorrect.do',
    data: {
      ...params,
      ...data,
    },
  };
};

/** 口算检查 获取批改结果 */
export const queryUserCorrectDetailByRecordId = (data: {
  recordId: string;
}): YTRequest<Record<string, any>> => {
  const params = {
    bookId: 528417, // 生产环境bookId
    ...data,
  };
  return {
    url: '/dallBook/queryUserCorrectDetailByRecordId.do',
    data: params,
  };
};

/** 获取口算检查历史记录 */
export const userCorrectHistory = (
  data: PaginationParams,
): YTRequest<ListDTOWithPagination<any>> => {
  return {
    url: '/dallBook/userCorrectHistory.do',
    data,
  };
};
