import {
  Y<PERSON><PERSON>,
  Y<PERSON><PERSON><PERSON>View,
  YTT<PERSON>t,
  YTTouchable,
  YTXStack,
  YTYStack,
} from '@bookln/cross-platform-components';
import { AppLoginType } from '@jgl/biz-func/api/usersApi';
import { useBizRouter } from '@jgl/biz-func/hooks/useBizRouter';
import { useIsUuidUser } from '@jgl/biz-func/redux/hooks';
import { store } from '@jgl/biz-func/redux/store';
import { AcIcon } from '@jgl/icon/src';
import { router } from '@jgl/utils/src/router';
import { showToast } from '@jgl/utils/src/toast';
import { ChevronRight } from '@tamagui/lucide-icons';
import { setStringAsync } from 'expo-clipboard';
import { Stack } from 'expo-router';
import { useAtom } from 'jotai';
import { useCallback, useMemo, useRef } from 'react';
import { Alert, TouchableOpacity } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { isParentVerifiedAtom } from '../../atom';
import {
  ContactServiceModal,
  ContactServiceModalRef,
} from '../../components/ContactServiceModal';
import { DebugSettingsButton } from '../../components/DebugSettingsButton';
import {
  OfficialAccountsModal,
  OfficialAccountsModalRef,
} from '../../components/OfficialAccountsModal';
import {
  UserGroupModal,
  UserGroupModalRef,
} from '../../components/UserGroupModal';
import { routerMap } from '../../utils/routerMap';

enum ListItemType {
  Record = 'record',
  Service = 'service',
  Setting = 'setting',
  EnterGroup = 'enterGroup',
  BindPhone = 'bindPhone',
  OfficialAccounts = 'officialAccounts',
  ParentMode = 'parentMode',
}

const listItems = [
  {
    key: ListItemType.Service,
    title: '联系客服',
    icon: AcIcon.IcService,
  },
  {
    key: ListItemType.ParentMode,
    title: '家长老师模式',
    icon: AcIcon.IcVerify,
  },
  {
    key: ListItemType.OfficialAccounts,
    title: '关注公众号',
    icon: AcIcon.IcWechat,
  },

  {
    key: ListItemType.Setting,
    title: '设置',
    icon: AcIcon.IcSetting,
  },
];

const MineScreen = () => {
  const safeAreaInsets = useSafeAreaInsets();
  const top = safeAreaInsets?.top || 0;
  const bizRouter = useBizRouter();

  const isUuidUser = useIsUuidUser();

  const userInfo = store.getState().userInfo;
  const isPhoneNumberBound = userInfo.phone != null;
  const isLoginWechat = userInfo.loginType === AppLoginType.WeChat;

  const contactModalRef = useRef<ContactServiceModalRef>(null);
  const userGroupModalRef = useRef<UserGroupModalRef>(null);
  const officialAccountsModalRef = useRef<OfficialAccountsModalRef>(null);

  const [isParentVerified, setIsParentVerified] = useAtom(isParentVerifiedAtom);

  const handlePressLogIn = useCallback(async () => {
    await bizRouter.push(`${routerMap.logInModal}`);
  }, [bizRouter]);

  const handleCopyUserId = useCallback(async () => {
    if (userInfo.userId) {
      try {
        await setStringAsync(String(userInfo.userId));
        showToast({ title: '用户ID已复制' });
      } catch (error) {
        showToast({ title: '复制失败' });
      }
    }
  }, [userInfo.userId]);

  /** 调整绑定手机号 */
  const handlePressBindPhone = useCallback(() => {
    if (isPhoneNumberBound) {
      showToast({ title: '已绑定手机号，暂不支持解绑' });
    } else {
      router.push(`${routerMap.bindPhone}`);
    }
  }, [isPhoneNumberBound]);

  const onPressListItem = useCallback(
    (type: ListItemType) => {
      switch (type) {
        case ListItemType.Record:
          router.push(`${routerMap.auditRecord}`);
          break;
        case ListItemType.BindPhone:
          handlePressBindPhone();
          break;
        case ListItemType.Service:
          contactModalRef.current?.showModal();
          break;
        case ListItemType.EnterGroup:
          userGroupModalRef.current?.showModal();
          break;
        case ListItemType.OfficialAccounts:
          officialAccountsModalRef.current?.showModal();
          break;
        case ListItemType.ParentMode:
          break;
        case ListItemType.Setting:
          router.push(`${routerMap.setting}`);
          break;
        default:
          break;
      }
    },
    [handlePressBindPhone],
  );

  const items = useMemo(() => {
    let arr = listItems;
    if (isLoginWechat && !isPhoneNumberBound) {
      arr = [...listItems];
    }

    return arr;
  }, [isLoginWechat, isPhoneNumberBound]);

  return (
    <>
      <Stack.Screen
        options={{
          navigationBarColor: '#F5F8FB',
          headerShown: false,
        }}
      />

      <YTYStack
        flex={1}
        width='100%'
        backgroundColor='#F5F8FB'
        paddingTop={top}
      >
        <YTXStack
          height={44}
          alignItems='center'
          justifyContent='center'
          paddingHorizontal={16}
          position='relative'
          backgroundColor='#F5F8FB'
        >
          <YTText color='#1F1F1F' fontWeight='bold' fontSize={18}>
            我的
          </YTText>
          <YTXStack
            position='absolute'
            right={16}
            height={44}
            flexDirection='row'
            alignItems='center'
            backgroundColor='#F5F8FB'
          >
            <DebugSettingsButton />
          </YTXStack>
        </YTXStack>
        <YTScrollView flex={1} contentInset={{ top: 8, bottom: 40 }}>
          <YTYStack paddingHorizontal={16} backgroundColor='#F5F8FB'>
            <YTXStack marginBottom={5} flexDirection='row' alignItems='center'>
              {isUuidUser ? (
                <YTXStack
                  flexDirection='row'
                  alignItems='center'
                  backgroundColor='#F5F8FB'
                >
                  <YTImage
                    source={require('../../assets/images/ic_user_avatar.png')}
                    w={56}
                    h={56}
                    borderRadius={28}
                    overflow='hidden'
                    marginRight={8}
                  />

                  <YTYStack flex={1} backgroundColor='#F5F8FB'>
                    <TouchableOpacity onPress={handlePressLogIn}>
                      <YTText color='#1F1F1F' fontWeight='bold' fontSize={17}>
                        点击登录
                      </YTText>
                    </TouchableOpacity>
                  </YTYStack>
                </YTXStack>
              ) : (
                <YTXStack
                  flexDirection='row'
                  alignItems='center'
                  backgroundColor='#F5F8FB'
                >
                  <YTImage
                    width={56}
                    height={56}
                    borderRadius={28}
                    marginRight={10}
                    source={
                      userInfo?.smallPhoto && userInfo.smallPhoto !== ''
                        ? { uri: userInfo.smallPhoto }
                        : require('../../assets/images/ic_user_avatar.png')
                    }
                    defaultSource={require('../../assets/images/ic_user_avatar.png')}
                  />
                  <YTYStack flex={1} backgroundColor='#F5F8FB'>
                    <YTText
                      color='#1F1F1F'
                      fontWeight='bold'
                      fontSize={17}
                      numberOfLines={1}
                    >
                      {userInfo?.nick ||
                        userInfo?.name ||
                        '微信昵称展示最多可以字数10字'}
                    </YTText>
                    <TouchableOpacity onPress={handleCopyUserId}>
                      <YTText color='#8C8C8C' fontSize={12} marginTop={4}>
                        ID：{userInfo?.userId ?? '-'}
                      </YTText>
                    </TouchableOpacity>
                  </YTYStack>
                </YTXStack>
              )}
            </YTXStack>
            <YTYStack
              backgroundColor='#fff'
              borderRadius={12}
              overflow='hidden'
              marginTop={16}
            >
              {items.map((item, index) => {
                const RowContent = (
                  <YTXStack
                    key={item.key}
                    flexDirection='row'
                    alignItems='center'
                    paddingHorizontal={16}
                    paddingVertical={14}
                    backgroundColor='#fff'
                    width={'$full'}
                  >
                    <YTImage height={20} width={20} source={item.icon} />
                    <YTText marginLeft={12} color='#1F1F1F' fontSize={16}>
                      {item.title}
                    </YTText>
                    <YTXStack
                      marginLeft='auto'
                      alignItems='center'
                      justifyContent='center'
                    >
                      {item.key === ListItemType.ParentMode ? (
                        <YTXStack
                          width={52}
                          height={28}
                          borderRadius={16}
                          backgroundColor={
                            isParentVerified ? '#1677FF' : '#BFBFBF'
                          }
                          padding={3}
                          onPress={() => {
                            if (isUuidUser) {
                              router.push(`${routerMap.logInModal}`);
                              return;
                            }
                            if (!isParentVerified) {
                              Alert.alert(
                                '开启家长认证',
                                '开启需家长认证，然后才可以查看解答',
                                [
                                  { text: '不开启', onPress: () => {} },
                                  {
                                    text: '开启',
                                    onPress: () => {
                                      router.push(`${routerMap.parentVerify}`);
                                    },
                                  },
                                ],
                              );
                            } else {
                              setIsParentVerified(false);
                            }
                          }}
                        >
                          <YTXStack
                            width={22}
                            height={22}
                            borderRadius={11}
                            backgroundColor='#fff'
                            marginLeft={isParentVerified ? 24 : 0}
                          />
                        </YTXStack>
                      ) : (
                        <ChevronRight size={20} color={'#8c8c8c'} />
                      )}
                    </YTXStack>
                  </YTXStack>
                );

                if (item.key === ListItemType.ParentMode) {
                  return RowContent;
                }
                return (
                  <YTTouchable
                    key={item.key}
                    activeOpacity={0.7}
                    onPress={() => onPressListItem(item.key)}
                  >
                    {RowContent}
                  </YTTouchable>
                );
              })}
            </YTYStack>
          </YTYStack>
        </YTScrollView>

        <ContactServiceModal ref={contactModalRef} />
        <UserGroupModal ref={userGroupModalRef} />
        <OfficialAccountsModal ref={officialAccountsModalRef} />
      </YTYStack>
    </>
  );
};

export default MineScreen;
