import React from 'react';
import { YTYStack } from '@bookln/cross-platform-components';
import { Stack, router } from 'expo-router';
import {
  useOralCorrection,
  OralCorrectionProgress,
} from '../hooks/useOralCorrection';
import { OralCorrectionCamera } from '../components/oralCorrection/OralCorrectionCamera';
import { OralCorrectionResult } from '../components/oralCorrection/OralCorrectionResult';

/**
 * 口算批改页面
 */
const OralCorrectionScreen = () => {
  const {
    progress,
    imageUrl,
    loading,
    checkDTO,
    submitCorrection,
    resetCorrection,
  } = useOralCorrection();

  const handleTakePhoto = (imageUri: string) => {
    submitCorrection(imageUri);
  };

  const handleRetake = () => {
    resetCorrection();
  };

  const handleViewHistory = () => {
    router.push('/oralCorrectionRecord');
  };

  const renderContent = () => {
    switch (progress) {
      case OralCorrectionProgress.Camera:
        return (
          <OralCorrectionCamera
            onTakePhoto={handleTakePhoto}
            loading={loading}
          />
        );

      case OralCorrectionProgress.Processing:
      case OralCorrectionProgress.Result:
        return (
          <OralCorrectionResult
            visible={true}
            imageUrl={imageUrl}
            checkDTO={checkDTO}
            loading={loading}
            onRetake={handleRetake}
            onViewHistory={handleViewHistory}
          />
        );

      default:
        return (
          <OralCorrectionCamera
            onTakePhoto={handleTakePhoto}
            loading={loading}
          />
        );
    }
  };

  return (
    <YTYStack flex={1} bg='white'>
      <Stack.Screen
        options={{
          title: '口算批改',
          headerBackTitle: '返回',
        }}
      />
      {renderContent()}
    </YTYStack>
  );
};

export default OralCorrectionScreen;
