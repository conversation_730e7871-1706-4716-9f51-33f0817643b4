import React, { useCallback, useEffect, useState } from 'react';
import {
  YTImage,
  YTText,
  YTTouchable,
  YTXStack,
  YTYStack,
  YTScrollView,
  YTView,
} from '@bookln/cross-platform-components';
import { useSafeAreaInsets } from '@jgl/biz-func';
import { container } from '@jgl/container';
import { router } from 'expo-router';
import { Stack } from 'expo-router';
import { userCorrectHistory } from '../api/api';

interface CorrectionRecord {
  id: string;
  recordId: string;
  sourceUrl: string;
  createTime: string;
  rightNums?: number;
  errorNums?: number;
}

interface GroupedRecords {
  [date: string]: CorrectionRecord[];
}

/**
 * 口算批改记录页面
 */
const OralCorrectionRecordScreen = () => {
  const { bottom } = useSafeAreaInsets();
  const [recordList, setRecordList] = useState<CorrectionRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 0,
    totalRecords: 0,
  });

  const fetchData = useCallback(
    async (isLoadMore = false) => {
      if (isLoadMore) {
        setLoadingMore(true);
      } else {
        setLoading(true);
      }

      try {
        const currentPage = isLoadMore ? pagination.currentPage + 1 : 1;
        const res = await container
          .net()
          .fetch(
            userCorrectHistory({
              pageSize: 20,
              currentPage,
            })
          );

        if (res.success && res.data) {
          const newRecords = res.data.list || [];
          
          if (isLoadMore) {
            setRecordList(prev => [...prev, ...newRecords]);
          } else {
            setRecordList(newRecords);
          }

          setPagination({
            currentPage: res.data.currentPage || currentPage,
            totalPages: res.data.totalPages || 0,
            totalRecords: res.data.totalRecords || 0,
          });
        }
      } catch (error) {
        console.error('获取批改记录失败:', error);
      } finally {
        if (isLoadMore) {
          setLoadingMore(false);
        } else {
          setLoading(false);
        }
      }
    },
    [pagination.currentPage]
  );

  useEffect(() => {
    fetchData();
  }, []);

  const handleClickImage = useCallback((recordId: string) => {
    // TODO: 跳转到批改结果详情页面
    console.log('查看详情:', recordId);
  }, []);

  const onScrollToBottom = useCallback(() => {
    if (!loadingMore && pagination.currentPage < pagination.totalPages) {
      fetchData(true);
    }
  }, [loadingMore, pagination.currentPage, pagination.totalPages, fetchData]);

  // 按日期分组记录
  const groupByDate = useCallback((records: CorrectionRecord[]): GroupedRecords => {
    return records.reduce((groups: GroupedRecords, record) => {
      const date = new Date(record.createTime).toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
      
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(record);
      return groups;
    }, {});
  }, []);

  const renderContent = () => {
    if (loading) {
      return (
        <YTYStack flex={1} ai="center" jc="center">
          <YTText fontSize={16} color="#666">
            加载中...
          </YTText>
        </YTYStack>
      );
    }

    if (recordList.length === 0) {
      return (
        <YTYStack flex={1} ai="center" jc="center">
          <YTText fontSize={16} color="#666">
            暂无批改记录
          </YTText>
        </YTYStack>
      );
    }

    const groupedRecords = groupByDate(recordList);
    const dateGroups = Object.entries(groupedRecords);

    return (
      <YTScrollView
        flex={1}
        showsVerticalScrollIndicator={false}
        onScrollEndDrag={(event) => {
          const { layoutMeasurement, contentOffset, contentSize } = event.nativeEvent;
          const isCloseToBottom = layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;
          if (isCloseToBottom) {
            onScrollToBottom();
          }
        }}
      >
        <YTYStack px={16} py={16}>
          {dateGroups.map(([date, records]) => (
            <YTYStack key={date} mb={24}>
              {/* 日期标题 */}
              <YTYStack mb={16}>
                <YTText fontSize={16} fontWeight="600" color="#1f1f1f">
                  {date}
                </YTText>
              </YTYStack>

              {/* 记录网格 */}
              <YTXStack flexWrap="wrap" gap={16}>
                {records.map((record, index) => (
                  <YTTouchable
                    key={record.id}
                    onPress={() => handleClickImage(record.recordId)}
                    style={{
                      width: 'calc(33.33% - 11px)',
                      aspectRatio: 105 / 140,
                    }}
                  >
                    <YTView
                      w="100%"
                      h="100%"
                      borderRadius={6}
                      borderWidth={1}
                      borderColor="#E5E6EB"
                      overflow="hidden"
                    >
                      <YTImage
                        source={{ uri: record.sourceUrl }}
                        style={{
                          width: '100%',
                          height: '100%',
                        }}
                        resizeMode="cover"
                      />
                    </YTView>
                  </YTTouchable>
                ))}
              </YTXStack>
            </YTYStack>
          ))}

          {/* 加载更多提示 */}
          {loadingMore && (
            <YTYStack ai="center" py={16}>
              <YTText fontSize={14} color="#999">
                加载中...
              </YTText>
            </YTYStack>
          )}

          {pagination.currentPage >= pagination.totalPages && recordList.length > 0 && (
            <YTYStack ai="center" py={16}>
              <YTText fontSize={14} color="#999">
                没有更多了
              </YTText>
            </YTYStack>
          )}
        </YTYStack>
      </YTScrollView>
    );
  };

  return (
    <YTYStack
      flex={1}
      bg="white"
      style={{ paddingBottom: bottom || 16 }}
    >
      <Stack.Screen
        options={{
          title: '批改记录',
          headerBackTitle: '返回',
        }}
      />
      {renderContent()}
    </YTYStack>
  );
};

export default OralCorrectionRecordScreen;
