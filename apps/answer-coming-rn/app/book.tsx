import {
  YT<PERSON><PERSON>on,
  Y<PERSON>mage,
  YTScrollView,
  YTStateView,
  YTText,
  YTTouchable,
  YTXStack,
  YTYStack,
} from '@bookln/cross-platform-components';
// eslint-disable-next-line import/no-duplicates
import { StyleSheet, Image, GestureResponderEvent } from 'react-native';
import { downloadContentByUrl } from '@jgl/biz-utils/src/downloadContentByUrl';
import { container } from '@jgl/container';
import { AcIcon } from '@jgl/icon/src';
import { parseJSON } from '@yunti-private/utils-universal';
import { useMount, useUnmount } from 'ahooks';
import { Stack, useLocalSearchParams } from 'expo-router';
import { isEmpty } from 'lodash-es';
import { useCallback, useEffect, useMemo, useState } from 'react';
import {
  dallBookHasCameraCutTask,
  getById,
  listSaasBookAudios,
  recordSearchHistory,
} from '../api/api';
import {
  BookDTO,
  BookHasCameraCutTaskDTO,
  ListeningOriginalAudioDTO,
  RecordSearchHistoryParams,
} from '../api/dto';
import { useDidShow } from '@jgl/utils/src/hooks/useDidShow';
import { storage } from '@jgl/utils/src/storage';
import { showToast } from '@jgl/utils/src/toast';
import { FlatList, View } from 'react-native';
import { BookInfo } from '../components/BookInfo'; // 单独组件，放在 components 目录
import {
  BookAnswerListItem,
  bookAnswerListItemWidth,
} from '../components/BookAnswerListItem';
import {
  listenAnswerListItemWidth,
  ListenOrginalTextListItem,
} from '../components/ListenOrginalTextListItem';
import { router } from '@jgl/utils';
import { routerMap } from '../utils/routerMap';
import { answerDetailHelper } from '../biz/answerDetail/AnswerDetailHelper';
import { url } from 'inspector';
import { useApiQuery } from '@yunti-private/net-query-hooks';
import { useIsUuidUser } from '@jgl/biz-func';

const itemSeparatorWidth = 8;

const styles = StyleSheet.create({
  flatList: {
    width: '100%',
  },
  itemSeparatorComponent: {
    width: itemSeparatorWidth,
  },
});

/** 答案页 */
export default function BookDetailScreen() {
  const [loading, setLoading] = useState(true);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [showLastViewIndex, setShowLastViewIndex] = useState(false);
  const [checkedDetail, setCheckedDetail] = useState(false);
  const { id } = useLocalSearchParams();
  const [bookDetail, setBookDetail] = useState<BookDTO>({});
  const [answerImgList, setAnswerImgList] = useState<string[]>([]);
  const [bookAudiosList, setBookAudiosList] = useState<
    ListeningOriginalAudioDTO[]
  >([]);

  const [photoCorrection, setPhotoCorrection] = useState<
    BookHasCameraCutTaskDTO | undefined
  >({});

  const [dataError, setDataError] = useState<Error | undefined>(undefined);
  const [cacheOriginTextOrder, setCacheOriginTextOrder] =
    useState<Record<string, number>>();

  const isUUIDUser = useIsUuidUser();

  /** 保存用户访问记录 */
  const handleSaveVisitRecord = useCallback(
    async (lastI?: number) => {
      const params: RecordSearchHistoryParams = {
        bookId: Number(id),
      };

      if (lastI !== undefined) {
        params.lastViewIndex = lastI;
      }

      await container.net().fetch(recordSearchHistory(params));
    },
    [id],
  );
  useApiQuery(
    recordSearchHistory,
    {
      bookId: Number(id),
      lastViewIndex: 0,
    },
    {
      enabled: !!id && !isUUIDUser,
    },
  );

  const [, setViewIndex] = useState<number[]>([]);

  const getViewIndex = useCallback((next: number) => {
    setViewIndex((preViewIndex) => {
      if (!preViewIndex.includes(next)) {
        const newViewIndex = Array.from(new Set([...preViewIndex, next]));
        return newViewIndex;
      }

      return preViewIndex;
    });
  }, []);

  const fetchBookAudios = useCallback(async () => {
    if (!id) {
      return;
    }
    const params = {
      bookId: id,
    };
    const res = await container.net().fetch(listSaasBookAudios(params));
    if (res.data) {
      setBookAudiosList(res.data);
    }
  }, [id]);

  const fetchAnswerInfo = useCallback(async () => {
    console.log('🚀 ~ BookDetailScreen ~ id:', id);
    if (id) {
      const res = await container
        .net()
        .fetch(getById({ id: Number(id) }))
        .finally(() => setLoading(false));
      const { success, data } = res;
      console.log('🚀 ~ BookDetailScreen ~ res:', res);
      if (success && data) {
        const { answer = '', lastViewIndex = 0 } = data;

        const show = data.lastViewIndex !== undefined;
        setShowLastViewIndex(show);
        setTimeout(() => {
          setCurrentIndex(lastViewIndex);
        }, 500);
        getViewIndex(lastViewIndex);
        // 获取听力原文
        fetchBookAudios();
        if (answer) {
          const result: any = await downloadContentByUrl({
            url: answer,
            contentType: 'text',
            net: container.net(),
          });

          if (result) {
            setAnswerImgList(JSON.parse(result) ?? []);
          }
        }

        setBookDetail(data);
        const bookHasCameraCutTask = await container
          .net()
          .fetch(dallBookHasCameraCutTask({ bookId: Number(id) }));
        setPhotoCorrection(bookHasCameraCutTask.data);
      } else {
        setDataError(new Error(res?.msg));
        res.msg &&
          showToast({
            title: res.msg,
          });
      }
    }
  }, [fetchBookAudios, getViewIndex, id]);

  useMount(async () => {
    fetchAnswerInfo();
  });

  useUnmount(() => {
    if (checkedDetail || currentIndex !== 0) {
      handleSaveVisitRecord(currentIndex);
    }
  });

  const handleJumpFromAnswer = useCallback(() => {}, []);

  useDidShow(async () => {
    const tempOriginTextOrder = parseJSON<Record<string, number>>(
      await storage.getItem('listenOriginalTextOrder'),
      'object',
    );
    setCacheOriginTextOrder(tempOriginTextOrder);
  });

  const [lastViewedIndex, setLastViewedIndex] = useState<number | null>(null);
  const [beLearning, setBeLearning] = useState<number | null>(null);

  // 为每本书生成唯一的存储键名
  const answerLastViewedKey = `lastViewedAnswerIndex_${id}`;
  const listenLastViewedKey = `beLearningIndex_${id}`;

  const getLastViewedIndex = useCallback(
    async (bookId: string): Promise<number | null> => {
      try {
        const key = answerLastViewedKey;
        const savedIndex = await storage.getItem(key);
        console.log(`获取到书籍 ${bookId} 上次查看的索引:`, savedIndex);
        return savedIndex ? parseInt(savedIndex, 10) : null;
      } catch (error) {
        console.error('获取上次查看索引失败:', error);
        return null;
      }
    },
    [answerLastViewedKey],
  );

  const handleAnswerPress = useCallback(
    (index: number) => {
      try {
        const key = answerLastViewedKey;
        storage.setItem(key, index.toString());
        setLastViewedIndex(index);
        answerDetailHelper.push({ bookId: id, defaultIndex: index });
      } catch (error) {
        console.error('Error saving last viewed:', error);
      }
    },
    [setLastViewedIndex, id, answerLastViewedKey],
  );

  useEffect(() => {
    const initializeData = async () => {
      const lastIndex = await getLastViewedIndex(id);
      if (lastIndex !== null) {
        setLastViewedIndex(lastIndex);
        console.log(`初始化时设置书籍 ${id} 上次查看索引:`, lastIndex);
      }
    };

    initializeData();
  }, [getLastViewedIndex, id]);

  const getBeLearningIndex = useCallback(
    async (bookId: string): Promise<number | null> => {
      try {
        const key = listenLastViewedKey;
        const savedIndex = await storage.getItem(key);
        console.log(`获取到书籍 ${bookId} 的学习索引:`, savedIndex);
        return savedIndex ? parseInt(savedIndex, 10) : null;
      } catch (error) {
        console.error('获取学习索引失败:', error);
        return null;
      }
    },
    [listenLastViewedKey],
  );

  const handleListenPress = useCallback(
    (dto: ListeningOriginalAudioDTO, index: number) => {
      try {
        const key = listenLastViewedKey;
        storage.setItem(key, index.toString());
        setBeLearning(index);
        // 记录点击的顺序，供听力原文页排序使用
        storage.setItem(
          'listenOriginalTextOrder',
          JSON.stringify({ [dto.id]: index }),
        );
        router.push(routerMap.listenOriginalText, {
          resId: dto.id,
          resIdSign: dto.idSign,
          title: dto.title,
        });
      } catch (error) {
        console.error('Error saving last viewed:', error);
      }
    },
    [id, listenLastViewedKey],
  );

  useEffect(() => {
    const initializeData = async () => {
      const lastIndex = await getBeLearningIndex(id);
      if (lastIndex !== null) {
        setBeLearning(lastIndex);
        console.log(`初始化时设置书籍 ${id} 学习索引:`, lastIndex);
      }
    };

    initializeData();
  }, [getBeLearningIndex, id]);
  const onPressToTakePhotoAndAnswer = useCallback(() => {
    router.push('/aiExplain');
  }, []);

  const content = useMemo(() => {
    return (
      <>
        <YTYStack backgroundColor={'white'}>
          <YTYStack className='p-[16PX]' backgroundColor={'white'}>
            <BookInfo bookDetail={bookDetail} />
          </YTYStack>
          <YTYStack backgroundColor={'white'}>
            <YTYStack
              className='flex items-center justify-between bg-white px-[16PX] py-[12PX]'
              backgroundColor={'white'}
            >
              <YTTouchable
                onPress={onPressToTakePhotoAndAnswer}
                style={{
                  height: 64,
                  marginHorizontal: 16,
                  borderRadius: 16,
                  borderWidth: 1,
                  borderColor: '#DEF2FF',
                  backgroundColor: '#F2FAFF',
                  paddingHorizontal: 16,
                  paddingVertical: 12,
                  marginTop: 16,
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Image
                  source={require('../assets/images/ic_take_for_answer.png')}
                  style={{
                    width: 40,
                    height: 40,
                    marginRight: 4,
                  }}
                />
                <YTText
                  style={{
                    fontSize: 16,
                    color: '#171717',
                    fontWeight: '500',
                  }}
                >
                  拍照答疑
                </YTText>
              </YTTouchable>
              {/* {photoCorrection?.id ? (
                <YTTouchable
                  className='ml-[16PX]  flex h-[64PX] w-full items-center justify-center  rounded-[16PX] border-[#FFE9E1] bg-[#FFF7F4] px-[16PX]  py-[16PX]'
                  marginTop={16}
                >
                  <YTImage
                    source={AcIcon.DetailPhoneCorrect}
                    className='h-[40PX] w-[40PX]'
                  />
                  <YTText className='font-normal' style={{ fontSize: 14 }}>
                    拍照批改
                  </YTText>
                </YTTouchable>
              ) : null} */}
            </YTYStack>

            <YTYStack marginTop={16} backgroundColor={'white'}>
              <YTYStack
                className='mb-[8PX] w-full px-[16PX]'
                height={22}
                paddingHorizontal={16}
                backgroundColor={'white'}
                marginBottom={8}
              >
                <YTText
                  fontFeatureSettings='PingFang SC'
                  backgroundColor={'white'}
                  style={{
                    height: 22,
                    fontSize: 14,
                    lineHeight: 22,
                    color: '#1C2024',
                  }}
                >
                  答案册
                </YTText>
              </YTYStack>

              {answerImgList.length > 0 ? (
                <FlatList
                  horizontal
                  getItemLayout={(data, index) => ({
                    length: bookAnswerListItemWidth + itemSeparatorWidth,
                    offset:
                      (bookAnswerListItemWidth + itemSeparatorWidth) * index,
                    index,
                  })}
                  initialScrollIndex={lastViewedIndex ?? 0}
                  showsHorizontalScrollIndicator={false}
                  style={styles.flatList}
                  contentContainerStyle={{
                    paddingHorizontal: 16,
                  }}
                  ItemSeparatorComponent={() => (
                    <View style={styles.itemSeparatorComponent} />
                  )}
                  data={answerImgList ?? []}
                  renderItem={(info) => {
                    const { item, index } = info;
                    return (
                      <BookAnswerListItem
                        answerImageUrl={item}
                        index={index}
                        isLastViewed={index === lastViewedIndex}
                        onPress={handleAnswerPress}
                      />
                    );
                  }}
                  keyExtractor={(item, index) => index.toString()}
                />
              ) : isEmpty(bookDetail) ? null : (
                <YTYStack jc={'center'} ai={'center'} backgroundColor={'white'}>
                  <Image
                    source={require('../assets/images/ic_book_no_answer.png')}
                  />
                  <YTText
                    marginTop={12}
                    color={'#595959'}
                    lineHeight={22}
                    fontSize={14}
                  >
                    本书暂无答案
                  </YTText>
                </YTYStack>
              )}
            </YTYStack>
          </YTYStack>

          {bookAudiosList.length > 0 ? (
            <YTYStack
              className='flex w-full flex-col'
              backgroundColor={'white'}
              style={{
                marginTop: 16,
              }}
            >
              <YTXStack
                className='mb-[8PX] w-full items-center px-[16PX]'
                backgroundColor={'white'}
                marginLeft={16}
                marginBottom={8}
              >
                <Image
                  source={require('../assets/images/shining.png')}
                  className='mr-[2PX] h-[20PX] w-[20PX]'
                />
                <YTText
                  className='text-text font-medium'
                  backgroundColor={'white'}
                  style={{ fontSize: 14 }}
                >
                  听力原文
                </YTText>
              </YTXStack>

              <FlatList
                horizontal
                getItemLayout={(data, index) => ({
                  length: listenAnswerListItemWidth + itemSeparatorWidth,
                  offset:
                    (listenAnswerListItemWidth + itemSeparatorWidth) * index,
                  index,
                })}
                initialScrollIndex={beLearning ?? 0}
                showsHorizontalScrollIndicator={false}
                style={styles.flatList}
                contentContainerStyle={{
                  paddingHorizontal: 16,
                }}
                ItemSeparatorComponent={() => (
                  <View style={styles.itemSeparatorComponent} />
                )}
                data={bookAudiosList ?? []}
                renderItem={(info) => {
                  const { item, index } = info;
                  return (
                    <ListenOrginalTextListItem
                      dto={item}
                      index={index}
                      isLearning={index === beLearning}
                      onPress={handleListenPress}
                    />
                  );
                }}
                keyExtractor={(item, index) => index.toString()}
              />
            </YTYStack>
          ) : null}
        </YTYStack>
      </>
    );
  }, [
    answerImgList,
    beLearning,
    bookAudiosList,
    bookDetail,
    handleAnswerPress,
    handleListenPress,
    lastViewedIndex,
    onPressToTakePhotoAndAnswer,
    photoCorrection?.id,
  ]);

  return (
    <>
      <Stack.Screen
        options={{
          headerShown: true,
          title: '',
          headerShadowVisible: false,
        }}
      />

      <YTScrollView flex={1} style={{ backgroundColor: '#FFFFFF' }}>
        <YTStateView
          isLoading={loading}
          error={dataError}
          onRetry={fetchAnswerInfo}
        >
          {content}
        </YTStateView>
      </YTScrollView>
    </>
  );
}
