fastlane documentation
----

# Installation

Make sure you have the latest version of the Xcode command line tools installed:

```sh
xcode-select --install
```

For _fastlane_ installation instructions, see [Installing _fastlane_](https://docs.fastlane.tools/#installing-fastlane)

# Available Actions

## iOS

### ios test

```sh
[bundle exec] fastlane ios test
```

Runs all the tests

### ios app_demo

```sh
[bundle exec] fastlane ios app_demo
```

书链demo

### ios app_sjj

```sh
[bundle exec] fastlane ios app_sjj
```

新东方书加加

### ios app

```sh
[bundle exec] fastlane ios app
```

打包

使用方法：bundle exec fastlane app app_id:10 is_inhouse:false is_dev:false

参数：is_inhouse，optional，是否是测试包，如果是测试包，则使用企业账号打包，默认false。

参数：is_dev，optional，是否开发签名的包，用于monkey测试等，默认false。

参数：should_send_message，optional，是否打包成功后发送钉钉消息，默认true。

参数：submit_for_review，optional，是否提交审核，默认false。

参数：upload_to_test_flight，optional，是否上传到TestFlight，默认false。

参数：match_force_update，Renew the provisioning profiles every time，默认true，手动执行的时候建议传false。

参数：is_debug，optional，是否是调试模式，默认false。

参数：base_host，optional，传入的值会替换为打包配置中的baseHost值。

参数: dev_build, optional, 是否开启 expo-dev-client，默认false

参数: runtime_version, optional, App 原生代码版本

参数: updates_url, optional, 热更新服务地址

参数: expo_update_config, optional, 热更新服务配置

参数: xcode_path, optional, Xcode 路径

参数: expo_release_channel, optional, 热更新服务配置

参数: app_version, optional, App 版本

### ios switch_app

```sh
[bundle exec] fastlane ios switch_app
```

切换应用

会切换所有配置，包括bundleId、应用名、图标等各种配置

使用方法：bundle exec fastlane switch_app app_id:10 is_inhouse:false is_dev:false

参数：is_inhouse，optional，是否是测试包，如果是测试包，则使用企业账号打包。

参数：is_dev，optional，是否开发签名的包，用于monkey测试等。

参数：base_host，optional，传入的值会替换为打包配置中的baseHost值。

参数：previous_vid，optional，获取之前版本的配置并替换，发布热更新的时候使用。

### ios update_certificates

```sh
[bundle exec] fastlane ios update_certificates
```

更新证书

更新证书目前是一个单独的任务，每一次都会强制更新，并且把新添加的设备也更新进去

会同时更新Company账号和Enterprise账号

Company账号下development、adhoc、appstore这三种类型都会更新

日常打包就不再每次更新证书，以提高打包速度

参数：app_id

### ios update_certificate_demo_wugensongai

```sh
[bundle exec] fastlane ios update_certificate_demo_wugensongai
```

更新证书

使用方法：bundle exec fastlane update_certificate_demo_wugensongai

### ios register_all_devices

```sh
[bundle exec] fastlane ios register_all_devices
```



### ios auto_detect_latest_archive_then_upload_to_testflight

```sh
[bundle exec] fastlane ios auto_detect_latest_archive_then_upload_to_testflight
```

自动检测最新生成的 archive 包是否有 appstore 的 ipa 包，如果有则上传至 testflight

使用方法: bundle exec fastlane auto_detect_latest_archive_then_upload_to_testflight

参数：app_id，required

参数: xcode_path, optional, Xcode 路径

参数: fastlane_path, optional, fastlane 路径

### ios modify_xcode_project_settings

```sh
[bundle exec] fastlane ios modify_xcode_project_settings
```

测试用 修改 Xcode build phase 和 build settings，确保 dev build 可以打出 jsbundle

### ios modify_expo_plist_runtime_version

```sh
[bundle exec] fastlane ios modify_expo_plist_runtime_version
```

更新 Expo.plist runtime_version

### ios modify_expo_plist_updates_url

```sh
[bundle exec] fastlane ios modify_expo_plist_updates_url
```

更新 Expo.plist updates_url

----

This README.md is auto-generated and will be re-generated every time [_fastlane_](https://fastlane.tools) is run.

More information about _fastlane_ can be found on [fastlane.tools](https://fastlane.tools).

The documentation of _fastlane_ can be found on [docs.fastlane.tools](https://docs.fastlane.tools).
