import os
import sys
import time
import requests
import subprocess
from hashlib import md5
import random
from urllib.request import urlretrieve
import shutil

local_apk_file_path = './app/build/outputs/apk/release/app-release.apk'

# 使用百度加固https://apkprotect.baidu.com/

# 暂时用不语的百度应用加固账号（每一个账号每天可以免费加固10个apk, 多个账号随机使用）
baidu_keys = [
        ("NdPDTHmszYJK2UC-", "6OKl0xMEDl518tIVCFp1H6rmp6eR8fu8"),
        ("3-nWy-Sm9lAKyBIS", "4T8LJtWz328qEsBL-hgha-l5m4HVtFjk"),
    ]

def jiagu(apkUrl, unsignedApkFileName, unsignedAlignedApkFileName, signedApkFileName):
    # 下载 reinforceApkUrl
    download_reinforce_apk_url= None
    
    try:download_reinforce_apk_url = sys.argv[2]
    except:pass
    
    if download_reinforce_apk_url:
        # 删除FOLDER_PATH下所有文件
        FOLDER_PATH = "./app/build/outputs/apk/release/"
        
        if os.path.exists(FOLDER_PATH):
            shutil.rmtree(FOLDER_PATH)

        os.makedirs(FOLDER_PATH, exist_ok=True)
        # 下载 reinforceApkUrl 并且直接签名
        TEMP_APK = os.path.join(FOLDER_PATH, f"{unsignedApkFileName}")

        print('开始下载APK到指定路径:', TEMP_APK)
        urlretrieve(download_reinforce_apk_url, TEMP_APK)
        print('APK下载完成:', TEMP_APK)
        # 签名
        print('开始对加固包签名')
        command_align = apkAlign_command(unsignedApkFileName,unsignedAlignedApkFileName)
        result = os.popen(command_align).read()
        print(result)
        command_sign = sign_command(unsignedAlignedApkFileName, signedApkFileName)
        result = os.popen(command_sign).read()
        print(result)
    else:
        if not os.path.isfile(apkUrl):
            print('Apk file not exist')
            exit(1)
        BAIDU_A_KEY, BAIDU_S_KEY = random.choice(baidu_keys)
        print('开始加固')
        output_path = './app/build/outputs/apk/release/%s' % unsignedApkFileName
        result = os.popen('apkprotect -type=free -akey %s -skey %s -i %s -o %s'%(BAIDU_A_KEY,BAIDU_S_KEY,apkUrl,output_path)).read()
        if not os.path.exists(output_path):
            print('加固失败')
            exit(1)
        else:
            print('加固成功，开始对加固包签名')
            command_align = apkAlign_command(unsignedApkFileName,unsignedAlignedApkFileName)
            result = os.popen(command_align).read()
            print(result)
            command_sign = sign_command(unsignedAlignedApkFileName, signedApkFileName)
            result = os.popen(command_sign).read()
            print(result)

# 计算文件的 md5值

def md5_file(name):
    m = md5()
    a_file = open(name, 'rb')  # 使用二进制格式读取文件内容
    m.update(a_file.read())
    a_file.close()
    return m.hexdigest()

# 使用apksigner进行签名


def sign_command(unsignedFileName, signedFileName):
    return 'apksigner sign --ks ./app/release.keystore --ks-key-alias kdtk --ks-pass pass:Yunti2014 --key-pass pass:Yunti2014 --v1-signing-enabled true --v2-signing-enabled true -v --out ./app/build/outputs/apk/release/%s ./app/build/outputs/apk/release/%s' % (signedFileName, unsignedFileName)

# APK文件对其
def apkAlign_command(unsignedApkFileName,unsignedAlignedApkFileName):
    return 'zipalign -p -f -v 4 ./app/build/outputs/apk/release/%s ./app/build/outputs/apk/release/%s' % (unsignedApkFileName, unsignedAlignedApkFileName)

def get_app_version(app_id):
    url = 'http://app-prepub.bookln.cn/appservice/miniappinfo.do?appid=%s&type=2' % app_id
    resp = requests.post(url)
    if resp.status_code == 200:
        version = resp.json()['data']['miniAPPVersion']
        if version.startswith('v'):
            return version 
        else:
            return 'v'+ version


if __name__ == '__main__':
    git_branch_shell = 'git symbolic-ref --short -q HEAD'
    git_commit_shell = 'git rev-parse --short HEAD'

    git_branch = subprocess.getoutput([git_branch_shell])
    git_commit = subprocess.getoutput([git_commit_shell])

    app_id = sys.argv[1]
    currentTimeStr = time.strftime('%Y%m%d%H%M%S')
    oss_file_key = 'tech/app/android/release/%s_%s.apk' % (
        app_id, currentTimeStr)
    unsignedApkFileName = '%s_%s_unsigned.apk' % (app_id, currentTimeStr)
    unsignedAlignedApkFileName = '%s_%s_unsigned_aligned.apk' % (app_id, currentTimeStr)
    appVersion=get_app_version(app_id)
    signedApkFileName = 'app_%s_release_base_%s_signed.apk' % (
            appVersion, app_id)
    
    jiagu(local_apk_file_path, unsignedApkFileName,unsignedAlignedApkFileName, signedApkFileName)    
        