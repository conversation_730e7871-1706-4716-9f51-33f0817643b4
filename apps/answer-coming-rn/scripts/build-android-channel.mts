#! /usr/bin/env zx
/* eslint-disable @typescript-eslint/ban-ts-comment */
import 'dotenv/config';
import { exit } from 'node:process';
import yargs from 'yargs';
import { $, cd } from 'zx';
import { getOSS } from './build-common.mts';

// 使用 yargs 解析命令行参数
const argv = yargs(process.argv.slice(2))
  .option('reinforceApkUrl', { type: 'string' })
  .parse();

// 使用异步 IIFE 初始化程序
(async () => {
  // 获取应用信息
  const appId = process.env.EXPO_PUBLIC_APP_ID?.toString();
  const reinforceApkUrl = argv.reinforceApkUrl as string;

  // 记录时间并返回结束时间函数
  const timeStart = (label) => {
    console.time(`⏱️ ${label}`);
    return () => console.timeEnd(`⏱️ ${label}`);
  };

  // 带超时的命令执行
  const execWithTimeout = async (cmd, timeoutMs = 600000) => {
    return Promise.race([
      cmd,
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('命令执行超时')), timeoutMs),
      ),
    ]);
  };

  /**
   * 准备 uv 环境
   */
  const uvEnvPreparation = async () => {
    console.log('✅ - build-android.mts - uvEnvPreparation - 开始准备 uv 环境');

    try {
      const endUvPrep = timeStart('UV 准备');
      await execWithTimeout(
        $`uv venv --python 3.10.18`,
        3 * 60 * 1000, // 3分钟超时
      );
      await execWithTimeout($`uv sync --upgrade`, 3 * 60 * 1000);
      console.log(
        '✅ - build-android.mts - uvEnvPreparation - uv 环境准备完成',
      );
      endUvPrep();
      return true;
    } catch (error) {
      console.error(
        `✅ - build-android.mts - uvEnvPreparation - 准备 uv 环境失败: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
      return false;
    }
  };

  /**
   * 导出 Android SDK 路径
   */
  const exportAndroidSDK = async () => {
    console.log(
      '✅ - build-android.mts - exportAndroidSDK - 设置 Android SDK 路径',
    );

    try {
      await $`if [ ! -f "local.properties" ]; then
        echo "sdk.dir=${
          process.env.HOME || '/Users/<USER>'
        }/Library/Android/sdk" >> local.properties
      fi`;
      console.log(
        '✅ - build-android.mts - exportAndroidSDK - Android SDK 路径设置完成',
      );
      return true;
    } catch (error) {
      console.error(
        `✅ - build-android.mts - exportAndroidSDK - 设置 Android SDK 路径失败: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
      return false;
    }
  };

  /**
   * 构建 Android 原生包
   */
  const bundleAndroid = async () => {
    console.log('✅ - build-android.mts - bundleAndroid - 开始构建 Android 包');

    try {
      // 标准构建逻辑
      console.log('✅ - build-android.mts - bundleAndroid - 执行标准构建流程');

      // 准备环境
      await uvEnvPreparation();
      await exportAndroidSDK();

      // 处理渠道配置
      await execWithTimeout(
        $`uv run python3 generate-channel-txt.py`,
        5 * 60 * 1000,
      );

      // Gradle 构建
      await $`commitHash='git rev-parse --short HEAD'`;
      await $`chmod +x gradlew`;

      cd('..');

      // 渠道包特殊处理
      await getOSS();
      cd('android');

      const endJiaguChannel = timeStart('加固渠道包');
      await execWithTimeout(
        $`uv run python3 jiagu-channel.py ${appId} ${reinforceApkUrl}`,
        20 * 60 * 1000,
      );
      endJiaguChannel();

      const endRebuildChannel = timeStart('重建渠道包');
      await execWithTimeout($`./gradlew reBuildChannel`, 15 * 60 * 1000);
      endRebuildChannel();

      cd('..');

      // 上传 APK
      await getOSS();
      cd('./scripts');

      const endUpload = timeStart('上传 release APK');
      await execWithTimeout(
        $`tsx ./upload-android-apk.mts ${appId} release false production`,
        10 * 60 * 1000,
      );
      endUpload();

      console.log('✅ - build-android.mts - bundleAndroid - 构建和上传完成');

      exit(0);
    } catch (error) {
      console.error(
        `✅ - build-android.mts - bundleAndroid - 构建失败: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
      exit(1);
    }
  };

  /**
   * 主运行逻辑
   */
  const run = async () => {
    try {
      // 如果是渠道包构建，则需要创建新的 expo 配置然后再进行构建

      // 判断 reinforceApkUrl 是否存在
      if (!reinforceApkUrl) {
        console.error(
          '✅ build-android.mts - run - reinforceApkUrl 不存在，中止构建',
        );
        exit(1);
      }
      // 执行构建
      cd('android');
      await bundleAndroid();
    } catch (error) {
      console.error(
        `✅ build-android.mts - run - 构建过程中发生错误: ${
          error instanceof Error ? error.message : String(error)
        }`,
      );
      exit(1);
    }
  };

  // 启动构建流程
  await run();
})();
