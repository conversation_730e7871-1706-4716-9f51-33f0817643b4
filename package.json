{"name": "jing<PERSON><PERSON>", "version": "1.0.0", "private": true, "description": "🐳鲸咕噜，一套代码适配H5、小程序、React Native", "packageManager": "pnpm@8.15.9", "scripts": {"postinstall": "node ./scripts/postinstall.mjs", "pnpm:devPreinstall": "echo 在安装依赖前统一pnpm和node版本 && node scripts/preinstall.mjs", "fix:mismatches": "echo 保持所有依赖版本统一，修改为最新的一个 && pnpm syncpack fix-mismatches", "fix:node18:openssl": "cross-env NODE_OPTIONS=--openssl-legacy-provider", "r": "echo reset，移除node_modules并安装依赖 && (pnpx rimraf node_modules || true) && pnpm i", "rm": "echo \"提交merge request\" && node rm.js", "mr": "echo \"提交merge request，不会随机@处理人\" && yunti mr", "mrr": "echo \"提交merge request并删除自己分支，不会随机@处理人\" && yunti mr -r", "up:api": "echo 更新API SDK && pnpm update @yunti-private/api-* --latest -r", "expo:check": "echo 用Expo检查react-native依赖，防止版本不兼容的情况 && pnpm expo install --check -- -w", "weapps:deploy": "echo 部署小程序，仅CI执行 && pnpm run fix:node18:openssl pnpm yunti deploy-weapps", "h5:deploy": "echo 部署h5，仅CI执行 && pnpm run fix:node18:openssl pnpm yunti deploy-apps", "jgl": "echo 开发鲸咕噜web、rn，按w打开网页 && pnpm --filter @jgl-app/jgl-rn start", "jgl:c": "echo 开发鲸咕噜web、rn（清除缓存），按w打开网页 && pnpm --filter @jgl-app/jgl start:clear", "jgl:web": "echo 开发鲸咕噜web && pnpm --filter @jgl-app/jgl-rn web", "jgl:ios": "echo 开发鲸咕噜iOS && (cd apps/jgl-rn && pnpm run ios)", "jgl:pod": "echo 安装鲸咕噜iOS原生依赖 && (cd apps/jgl-rn && pnpm run pod)", "jgl:pod:c": "echo 清除并安装鲸咕噜RN iOS原生依赖 && (cd apps/jgl-rn && pnpm run pod:c)", "jgl:android": "echo 开发鲸咕噜Android && pnpm --filter @jgl-app/jgl-rn android", "jgl:xcode": "echo 用Xcode打开鲸咕噜iOS原生工程 && pnpm --filter @jgl-app/jgl-rn xcode", "jgl:as": "echo 用Android Studio打开鲸咕噜Android原生工程 && pnpm --filter @jgl-app/jgl-rn as", "jgl:dev:weapp": "pnpm run fix:node18:openssl pnpm --filter jgl dev:weapp", "jgl:dev:h5": "pnpm run fix:node18:openssl pnpm --filter jgl dev:h5", "jgl:dev:rn": "echo 开发鲸咕噜RN，Windows、macOS可用 && pnpm --filter @jgl-app/jgl-rn start", "jgl:dev:rn:c": "echo 开发鲸咕噜RN，清除缓存，Windows、macOS可用 && pnpm --filter @jgl-app/jgl-rn start:clear", "jgl:dev:rn:r": "echo 开发鲸咕噜RN，删除node_modules，清除缓存，Windows、macOS可用 && pnpm r && pnpm --filter @jgl-app/jgl-rn start:clear", "jgl:dev:ios": "echo 开发鲸咕噜RN，编译iOS原生代码并启动iOS模拟器，macOS可用，需安装Xcode && (cd apps/jgl-rn && pnpm run ios)", "jgl:dev:ios:init": "echo 如果提示baseInfo.json找不到就可以执行一下 && (cd apps/jgl-rn && pnpm run ios:init)", "jgl:dev:android": "echo 开发鲸咕噜RN，编译Android原生代码并启动Android真机或模拟器，Windows、macOS可用，需配置Android开发环境 && pnpm --filter @jgl-app/jgl-rn android", "jgl:dev:build": "echo 鲸咕噜RN开发包 && pnpm jgl:dev:build:ios && pnpm jgl:dev:build:android", "jgl:dev:build:ios": "echo \"鲸咕噜RN iOS开发包\" && pnpx open-cli http://ci.jump.yeteam.com:8080/view/%E9%B2%B8%E5%92%95%E5%99%9C/job/jgl-ios-dev/build?token=JGL_DEV_CLIENT", "jgl:dev:build:android": "echo \"鲸咕噜RN Android开发包\" && pnpx open-cli http://ci.jump.yeteam.com:8080/view/%E9%B2%B8%E5%92%95%E5%99%9C/job/jgl-android-dev/build?token=JGL_DEV_CLIENT", "jgl:deploy:ci": "pnpm run --filter jgl deploy:ci", "jgl:build:weapp": "pnpm run fix:node18:openssl pnpm --filter jgl build:weapp", "jgl:build:weapp:size": "echo build并分析bundle大小 && pnpm --filter jgl build:weapp:size", "jgl:build:weapp:upload": "pnpm --filter jgl build:weapp:upload", "jgl:build:h5": "pnpm run fix:node18:openssl pnpm --filter jgl build:h5", "jgl:build:ios": "echo \"鲸咕噜RN iOS测试包\" && pnpm --filter @jgl-app/jgl-rn build:ios", "jgl:build:ios:dev": "echo \"鲸咕噜RN iOS开发包\" && pnpm --filter @jgl-app/jgl-rn build:ios:dev", "jgl:build:ios:upload": "echo \"上传鲸咕噜RN iOS测试包到 TestFlight\" && pnpm --filter @jgl-app/jgl-rn build:ios:upload", "jgl:build:android": "echo \"鲸咕噜RN Android测试包\" && pnpm --filter @jgl-app/jgl-rn build:android", "jgl:build:android:dev": "echo \"鲸咕噜RN Android开发包\" && pnpm --filter @jgl-app/jgl-rn build:android:dev", "jgl:build:android:release": "echo \"鲸咕噜RN Android渠道包\" && pnpm --filter @jgl-app/jgl-rn build:android:release", "jgl:bundlejs:ios": "echo 使用 react native bundle 命令输出 ios 平台 js bundle 包 && pnpm --filter @jgl-app/jgl-rn bundlejs:ios", "jgl:bundlejs:android": "echo 使用 react native bundle 命令输出 android 平台 js bundle 包 && pnpm --filter @jgl-app/jgl-rn bundlejs:android", "dall": "echo 开发答案来了web、rn，按w打开网页 && pnpm --filter @dall-app/dall-rn start", "dall:c": "echo 开发答案来了web、rn（清除缓存），按w打开网页 && pnpm --filter @dall-app/dall-rn start:clear", "dall:web": "echo 开发答案来了web && pnpm --filter @dall-app/dall-rn web", "dall:ios": "echo 开发答案来了iOS && pnpm --filter @dall-app/dall-rn ios", "dall:ios:cert": "(cd ./apps/answer-coming-rn/ios && bundle exec fastlane match adhoc --git_branch 3X254MM3PQ --team_id=3X254MM3PQ --app_identifier cn.xinqi.dall --username <EMAIL> --git_url https://git.bookln.cn/yuntitech_react_native/ios-certificates --clone_branch_directly --generate_apple_certs false)", "dall:pod": "echo 安装答案来了iOS原生依赖 && pnpm --filter @dall-app/dall-rn pod", "dall:pod:c": "echo 清除并安装答案来了RN iOS原生依赖 && pnpm --filter @dall-app/dall-rn pod:c", "dall:android": "echo 开发答案来了Android && pnpm --filter @dall-app/dall-rn android", "dall:xcode": "echo 用Xcode打开答案来了iOS原生工程 && pnpm --filter @dall-app/dall-rn xcode", "dall:as": "echo 用Android Studio打开答案来了Android原生工程 && pnpm --filter @dall-app/dall-rn as", "dall:build:web": "", "dall:build:ios": "echo \"答案来了RN iOS测试包\" && pnpm --filter @dall-app/dall-rn build:ios", "dall:build:ios:dev": "echo \"答案来了RN iOS开发包\" && pnpm --filter @dall-app/dall-rn build:ios:dev", "dall:build:ios:upload": "echo \"上传答案来了RN iOS测试包到 TestFlight\" && pnpm --filter @dall-app/dall-rn build:ios:upload", "dall:build:android": "echo \"答案来了RN Android测试包\" && pnpm --filter @dall-app/dall-rn build:android", "dall:build:android:dev": "echo \"答案来了RN Android开发包\" && pnpm --filter @dall-app/dall-rn build:android:dev", "dall:build:android:release": "echo \"答案来了RN Android渠道包\" && pnpm --filter @dall-app/dall-rn build:android:release", "dall:build:android:channel": "echo \"通过加固APK打书链RN Android渠道\" && pnpm --filter @dall-app/dall-rn build:android:channel", "dall:bundlejs:ios": "echo 使用 react native bundle 命令输出 ios 平台 js bundle 包 && pnpm --filter @dall-app/dall-rn bundlejs:ios", "dall:bundlejs:android": "echo 使用 react native bundle 命令输出 android 平台 js bundle 包 && pnpm --filter @dall-app/dall-rn bundlejs:android", "chat:build:weapp": "pnpm --filter chat build:weapp", "chat:build:h5": "pnpm --filter chat build:h5", "chat:build:rn": "pnpm --filter chat build:rn", "chat:dev": "pnpm run fix:node18:openssl ttab pnpm chat:dev:h5; ttab pnpm chat:dev:weapp; ttab pnpm chat:dev:rn", "chat:dev:weapp": "pnpm run fix:node18:openssl pnpm --filter chat dev:weapp", "chat:dev:h5": "pnpm run fix:node18:openssl pnpm --filter chat dev:h5", "chat:dev:rn": "pnpm --filter chat dev:rn", "lan:build:weapp": "pnpm run fix:node18:openssl pnpm --filter language build:weapp", "lan:build:h5": "pnpm run fix:node18:openssl pnpm --filter language build:h5", "lan:build:rn": "pnpm --filter language build:rn", "lan:dev": "pnpm run fix:node18:openssl ttab pnpm language:dev:h5; ttab pnpm language:dev:weapp; ttab pnpm language:dev:rn", "lan:dev:weapp": "pnpm run fix:node18:openssl pnpm --filter language dev:weapp", "lan:dev:h5": "pnpm run fix:node18:openssl pnpm --filter language dev:h5", "lan:dev:rn": "echo 开发学英语RN，Windows、macOS可用 && pnpm --filter @jgl-app/language-rn start", "lan:dev:rn:c": "echo 开发学英语RN，清除缓存，Windows、macOS可用 && pnpm --filter @jgl-app/language-rn start:clear", "lan:dev:rn:r": "echo 开发学英语RN，删除node_modules，清除缓存，Windows、macOS可用 && pnpm r && pnpm --filter @jgl-app/language-rn start:clear", "lan:dev:ios": "echo 开发学英语RN，编译iOS原生代码并启动iOS模拟器，macOS可用，需安装Xcode && pnpm --filter @jgl-app/language-rn ios", "lan:dev:android": "echo 开发学英语RN，编译Android原生代码并启动Android真机或模拟器，Windows、macOS可用，需配置Android开发环境 && pnpm --filter @jgl-app/language-rn android", "lan:dev:build": "echo 学英语RN开发包 && pnpm lan:dev:build:ios && pnpm lan:dev:build:android", "lan:dev:build:ios": "echo \"学英语RN iOS开发包\" && pnpx open-cli http://ci.jump.yeteam.com:8080/view/%E9%B2%B8%E5%92%95%E5%99%9CApp/job/jgl-ios-dev/build?token=JGL_DEV_CLIENT", "lan:build:ios": "echo \"学英语RN iOS测试包\" && pnpm --filter @jgl-app/language-rn build:ios", "lan:build:ios:dev": "echo \"学英语RN iOS开发包\" && pnpm --filter @jgl-app/language-rn build:ios:dev", "lan:build:ios:upload": "echo \"上传学英语RN iOS测试包到 TestFlight\" && pnpm --filter @jgl-app/language-rn build:ios:upload", "lan:build:android": "echo \"学英语RN Android测试包\" && pnpm --filter @jgl-app/language-rn build:android", "lan:build:android:dev": "echo \"学英语RN Android开发包\" && pnpm --filter @jgl-app/language-rn build:android:dev", "lan:build:android:release": "echo \"学英语RN Android渠道包\" && pnpm --filter @jgl-app/language-rn build:android:release", "lan:dev:build:android": "echo \"学英语RN Android开发包\" && pnpx open-cli http://ci.jump.yeteam.com:8080/view/%E9%B2%B8%E5%92%95%E5%99%9CApp/job/jgl-android-dev/build?token=JGL_DEV_CLIENT", "lan:xcode": "echo 使用Xcode打开学英语RN iOS原生工程，macOS可用 && pnpm --filter @jgl-app/language-rn xcode", "lan:pod": "echo 安装学英语RN iOS原生依赖 && pnpm --filter @jgl-app/language-rn pod", "lan:pod:c": "echo 清除并安装学英语RN iOS原生依赖 && pnpm --filter @jgl-app/language-rn pod:c", "lan:as": "echo 使用Android Studio打开学英语RN Android原生工程，macOS可用 && pnpm --filter @jgl-app/language-rn as", "lan:bundlejs:ios": "echo 使用 react native bundle 命令输出 ios 平台 js bundle 包 && pnpm --filter @jgl-app/language-rn bundlejs:ios", "lan:bundlejs:android": "echo 使用 react native bundle 命令输出 android 平台 js bundle 包 && pnpm --filter @jgl-app/language-rn bundlejs:android", "speaking:build:weapp": "pnpm run fix:node18:openssl pnpm --filter speaking build:weapp", "speaking:build:h5": "pnpm run fix:node18:openssl pnpm --filter speaking build:h5", "speaking:build:rn": "pnpm --filter speaking build:rn", "speaking:dev": "pnpm run fix:node18:openssl ttab pnpm speaking:dev:h5; ttab pnpm speaking:dev:weapp; ttab pnpm speaking:dev:rn", "speaking:dev:weapp": "pnpm run fix:node18:openssl pnpm --filter speaking dev:weapp", "speaking:dev:h5": "pnpm run fix:node18:openssl pnpm --filter speaking dev:h5", "speaking:dev:rn": "pnpm --filter speaking dev:rn", "yqa:build:weapp": "pnpm run fix:node18:openssl pnpm --filter yqasxx build:weapp", "yqa:dev:weapp": "pnpm run fix:node18:openssl pnpm --filter yqasxx dev:weapp", "yqa:switch": "echo \"切换一起爱上学习，appId见utils.mts文件，如：pnpm yqa:switch 98\" && pnpm --filter yqasxx switch", "learnNum:build:weapp": "pnpm run fix:node18:openssl pnpm --filter learn-number build:weapp", "learnNum:dev:weapp": "pnpm run fix:node18:openssl pnpm --filter learn-number dev:weapp", "chi:build:weapp": "pnpm run fix:node18:openssl pnpm --filter chinese build:weapp", "chi:build:h5": "pnpm run fix:node18:openssl pnpm --filter chinese build:h5", "chi:dev:weapp": "pnpm run fix:node18:openssl pnpm --filter chinese dev:weapp", "chi:dev:h5": "pnpm run fix:node18:openssl pnpm --filter chinese dev:h5", "chi:dev:rn": "pnpm --filter @jgl-app/chinese-rn start", "chi:dev:ios": "pnpm --filter @jgl-app/chinese-rn ios", "chi:dev:android": "pnpm --filter @jgl-app/chinese-rn android", "dall:dev:rn": "echo 开发答案来了RN，Windows、macOS可用 && pnpm --filter @dall-app/dall-rn start", "dall:dev:rn:c": "echo 开发答案来了RN，清除缓存，Windows、macOS可用 && pnpm --filter @dall-app/dall-rn start:clear", "dall:dev:rn:r": "echo 开发答案来了RN，删除node_modules，清除缓存，Windows、macOS可用 && pnpm r && pnpm --filter @dall-app/dall-rn start:clear", "dall:dev:ios": "echo 开发答案来了RN，编译iOS原生代码并启动iOS模拟器，macOS可用，需安装Xcode && pnpm --filter @dall-app/dall-rn ios", "dall:dev:android": "echo 开发答案来了RN，编译Android原生代码并启动Android真机或模拟器，Windows、macOS可用，需配置Android开发环境 && pnpm --filter @dall-app/dall-rn android", "dall:dev:build": "echo 答案来了RN开发包 && pnpm jgl:dev:build:ios && pnpm jgl:dev:build:android", "dall:dev:build:ios": "echo \"答案来了RN iOS开发包\" && pnpx open-cli http://ci.jump.yeteam.com:8080/view/%E7%AD%94%E6%A1%88%E6%9D%A5%E4%BA%86/job/dall-ios-dev/build?token=DALL_DEV_CLIENT", "dall:dev:build:android": "echo \"答案来了RN Android开发包\" && pnpx open-cli http://ci.jump.yeteam.com:8080/view/%E7%AD%94%E6%A1%88%E6%9D%A5%E4%BA%86/job/dall-android-dev/build?token=DALL_DEV_CLIENT", "qa:build:weapp": "pnpm run fix:node18:openssl pnpm --filter questions-and-answers build:weapp", "qa:dev:weapp": "pnpm run fix:node18:openssl pnpm --filter questions-and-answers dev:weapp", "cloudPrint:build:weapp": "pnpm --filter cloud-print build:weapp", "cloudPrint:dev:weapp": "pnpm --filter cloud-print dev:weapp", "chb:build:weapp": "pnpm run fix:node18:openssl pnpm --filter class-home-book build:weapp", "chb:dev:weapp": "pnpm run fix:node18:openssl pnpm --filter class-home-book dev:weapp", "dall:dev:weapp": "echo 开发答案来了小程序 && pnpm run fix:node18:openssl pnpm --filter dall dev:weapp", "dall:dev:weapp:dab": "echo 开发答案宝小程序 && pnpm run fix:node18:openssl pnpm --filter dall dev:weapp:dab", "dall:build:weapp": "pnpm run fix:node18:openssl pnpm --filter dall build:weapp", "dall:build:weapp:dab": "pnpm run fix:node18:openssl pnpm --filter dall build:weapp:dab", "dall:dev:h5": "pnpm run fix:node18:openssl pnpm --filter dall dev:h5", "dall:build:h5": "pnpm run fix:node18:openssl pnpm --filter dall build:h5", "syncpack:fix": "pnpm syncpack fix-mismatches", "ar:dev:weapp": "pnpm run fix:node18:openssl pnpm --filter ar dev:weapp", "ar:build:weapp": "pnpm run fix:node18:openssl pnpm --filter ar build:weapp", "ar:build:weapp:upload": "pnpm --filter ar build:weapp:upload", "point:dev:weapp": "pnpm run fix:node18:openssl pnpm --filter point-and-read dev:weapp", "point:dev:h5": "pnpm run fix:node18:openssl pnpm --filter point-and-read dev:h5", "point:build:weapp": "pnpm run fix:node18:openssl pnpm --filter point-and-read build:weapp", "point:build:weapp:upload": "pnpm --filter point-and-read build:weapp:upload", "as:dev:weapp": "pnpm run fix:node18:openssl pnpm --filter ai-self dev:weapp", "as:build:weapp": "pnpm run fix:node18:openssl pnpm --filter ai-self build:weapp", "as:build:weapp:upload": "pnpm --filter ai-self build:weapp:upload", "as:build:weapp:size": "echo build并分析bundle大小 && pnpm --filter ai-self build:weapp:size", "check:unused:icon": "echo 检查 packages/icon/src/index.ts 中的是否有未用到 Icon && npx tsx scripts/analyze-unused-icons.ts", "check:unused:asIcon": "echo 检查 packages/icon/src/asIcon.ts 中的是否有未用到 asIcon && npx tsx scripts/analyze-unused-icons.ts -f packages/icon/src/asIcon.ts -p asIcon", "check:unused:arIcon": "echo 检查 packages/icon/src/arIcon.ts 中的是否有未用到 arIcon && npx tsx scripts/analyze-unused-icons.ts -f packages/icon/src/arIcon.ts -p arIcon", "check:unused:aiQAIcon": "echo 检查 packages/icon/src/aiQAIcon.ts 中的是否有未用到 aiQAIcon && npx tsx scripts/analyze-unused-icons.ts -f packages/icon/src/aiQAIcon.ts -p aiQAIcon", "check:unused:acIcon": "echo 检查 packages/icon/src/index.ts 中的是否有未用到的 AcIcon && npx tsx scripts/analyze-unused-icons.ts -f packages/icon/src/index.ts -p AcIcon"}, "devDependencies": {"@babel/cli": "7.23.0", "@babel/core": "7.27.4", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-transform-flow-strip-types": "7.26.5", "@babel/plugin-transform-optional-catch-binding": "7.24.1", "@babel/plugin-transform-optional-chaining": "7.23.4", "@babel/plugin-transform-private-methods": "7.25.9", "@babel/preset-env": "7.23.2", "@babel/preset-typescript": "7.24.1", "@biomejs/biome": "1.9.4", "@ctrl/tinycolor": "4.0.2", "@expo/config": "11.0.13", "@larksuiteoapi/node-sdk": "1.22.0", "@nx/devkit": "21.2.2", "@nx/eslint-plugin": "21.2.2", "@pmmmwh/react-refresh-webpack-plugin": "0.5.10", "@tailwindcss/line-clamp": "0.4.4", "@tamagui/babel-plugin": "1.129.11", "@tamagui/metro-plugin": "1.129.11", "@tanstack/eslint-plugin-query": "5.68.0", "@tarojs/cli": "3.6.15", "@tarojs/plugin-mini-ci": "3.6.26", "@tarojs/taro-loader": "3.6.15", "@tarojs/webpack5-runner": "3.6.15", "@testing-library/react": "15.0.7", "@types/ali-oss": "6.16.11", "@types/archiver": "6.0.2", "@types/crypto-js": "4.1.1", "@types/form-data": "2.5.0", "@types/howler": "2.2.9", "@types/jest": "29.5.12", "@types/lodash-es": "4.17.12", "@types/marked": "5.0.0", "@types/node": "18.15.6", "@types/qrcode": "1.5.5", "@types/react": "19.0.14", "@types/react-dom": "18.3.7", "@types/react-native-canvas": "0.1.14", "@types/react-native-snap-carousel": "3.8.10", "@types/react-syntax-highlighter": "15.5.13", "@types/redux-logger": "3.0.9", "@types/rn-fetch-blob": "1.2.7", "@types/uuid": "3.4.0", "@types/webpack-bundle-analyzer": "4.7.0", "@types/webpack-env": "1.18.0", "@types/xml2js": "0.4.14", "@typescript-eslint/eslint-plugin": "6.21.0", "@typescript-eslint/parser": "6.21.0", "@vitest/coverage-v8": "3.0.5", "@yunti-private/antd-tailwind-presets": "1.0.8", "@yunti-private/api-bizboot": "0.0.1185", "@yunti-private/cli": "0.1.26", "ali-oss": "6.18.1", "autoprefixer": "10.4.16", "babel-jest": "29.7.0", "babel-loader": "8.2.1", "babel-plugin-import": "1.13.6", "babel-preset-taro": "3.6.15", "cache-loader": "4.1.0", "color-convert": "2.0.1", "cross-env": "7.0.3", "dingtalk-robot-sender": "1.2.0", "dotenv-flow": "4.1.0", "eslint": "8.57.1", "eslint-config-prettier": "9.1.0", "eslint-config-taro": "3.6.15", "eslint-plugin-import": "2.29.0", "eslint-plugin-react": "7.33.2", "eslint-plugin-react-hooks": "4.6.0", "expo-modules-autolinking": "2.1.14", "fs-extra": "11.2.0", "git-lab-cli": "2.0.7", "identity-obj-proxy": "3.0.0", "install-peerdeps": "3.0.3", "jest": "29.7.0", "jest-expo": "53.0.9", "less": "4.1.3", "less-loader": "11.1.0", "metro": "0.82.4", "node-gyp-build": "4.6.0", "nx": "21.2.2", "open-cli": "7.2.0", "postcss": "8.4.23", "postcss-rem-to-responsive-pixel": "6.0.1", "prettier": "3.0.3", "prettier-plugin-tailwindcss": "0.5.6", "qrcode": "1.5.3", "react-native-calendars": "1.1305.0", "react-native-haptic-feedback": "2.3.3", "react-native-less-transformer": "1.4.0", "react-refresh": "0.11.0", "react-test-renderer": "18.2.0", "reactotron-react-native": "5.0.4", "redux-logger": "3.0.6", "rimraf": "4.4.1", "stylelint": "14.16.1", "syncpack": "12.3.3", "tailwind-merge": "2.1.0", "tailwindcss": "3.3.2", "taro-plugin-compiler-optimization": "1.0.4", "thread-loader": "4.0.1", "translation-check": "1.0.3", "ts-babel": "6.1.7", "ts-jest": "29.1.2", "ts-node": "10.9.1", "tsconfig-paths-webpack-plugin": "4.0.1", "tsx": "4.19.0", "typescript": "5.8.3", "weapp-tailwindcss": "3.7.0", "webpack": "5.69.0", "webpack-bundle-analyzer": "4.8.0", "xml2js": "0.6.2", "yargs": "17.7.2", "zx": "7.2.1"}, "dependencies": {"@antmjs/vantui": "3.6.5", "@babel/runtime": "7.21.0", "@bam.tech/react-native-image-resizer": "3.0.11", "@bookln/ai-explain": "workspace:*", "@bookln/biz-components": "workspace:*", "@bookln/biz-components-rojer-katex-mini": "workspace:*", "@bookln/biz-func": "workspace:*", "@bookln/cross-platform-components": "workspace:*", "@bookln/icon-custom": "workspace:*", "@bookln/icon-lucide": "workspace:*", "@bookln/iconsax": "workspace:*", "@bookln/permission": "workspace:*", "@config-plugins/react-native-blob-util": "6.0.0", "@config-plugins/react-native-pdf": "11.0.0", "@expo/react-native-action-sheet": "4.1.1", "@gorhom/bottom-sheet": "5.1.6", "@jgl-scene/word-podcast": "workspace:*", "@jgl/ai-learn": "workspace:*", "@jgl/ai-qa": "workspace:*", "@jgl/biz-components": "workspace:*", "@jgl/biz-components-rojer-katex-mini": "workspace:*", "@jgl/biz-func": "workspace:*", "@jgl/biz-utils": "workspace:*", "@jgl/components": "workspace:*", "@jgl/container": "workspace:*", "@jgl/icon": "workspace:*", "@jgl/im": "workspace:*", "@jgl/logger": "workspace:*", "@jgl/scene-base": "workspace:*", "@jgl/scene-components": "workspace:*", "@jgl/scene-constants": "workspace:*", "@jgl/scene-hooks": "workspace:*", "@jgl/ui": "workspace:*", "@jgl/ui-v4": "workspace:*", "@jgl/upload": "workspace:*", "@jgl/utils": "workspace:*", "@lodev09/react-native-exify": "0.2.7", "@nutui/nutui-react-taro": "2.3.6", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/hooks": "100.1.0", "@react-native-community/netinfo": "11.4.1", "@react-native-voice/voice": "3.2.4", "@react-navigation/drawer": "7.4.1", "@react-navigation/native": "7.1.10", "@reduxjs/toolkit": "1.9.3", "@rneui/base": "4.0.0-rc.8", "@rneui/themed": "4.0.0-rc.8", "@rojer/katex-mini": "1.1.3", "@shopify/flash-list": "1.7.6", "@shopify/react-native-skia": "2.0.0-next.4", "@sleiv/react-native-graceful-exit": "0.1.0", "@tamagui/config": "1.129.11", "@tamagui/create-theme": "1.129.11", "@tamagui/lucide-icons": "1.129.11", "@tamagui/next-theme": "1.129.11", "@tamagui/shorthands": "1.129.11", "@tamagui/themes": "1.129.11", "@tamagui/toast": "1.129.11", "@tarojs/components": "3.6.15", "@tarojs/components-rn": "3.6.15", "@tarojs/helper": "3.6.15", "@tarojs/plugin-framework-react": "3.6.15", "@tarojs/plugin-html": "3.6.15", "@tarojs/plugin-platform-alipay": "3.6.15", "@tarojs/plugin-platform-h5": "3.6.15", "@tarojs/plugin-platform-jd": "3.6.15", "@tarojs/plugin-platform-qq": "3.6.15", "@tarojs/plugin-platform-swan": "3.6.15", "@tarojs/plugin-platform-tt": "3.6.15", "@tarojs/plugin-platform-weapp": "3.6.15", "@tarojs/react": "3.6.15", "@tarojs/rn-runner": "3.6.15", "@tarojs/rn-supporter": "3.6.15", "@tarojs/runtime": "3.6.15", "@tarojs/runtime-rn": "3.6.15", "@tarojs/shared": "3.6.15", "@tarojs/taro": "3.6.15", "@tarojs/taro-rn": "3.6.15", "@types/lodash-webpack-plugin": "0.11.9", "@yunti-private/api-booklnboot": "0.0.1097", "@yunti-private/api-gulu": "0.0.794", "@yunti-private/api-xingdeng-boot": "0.0.1230", "@yunti-private/basic-imds": "1.0.25", "@yunti-private/connection": "1.2.10", "@yunti-private/env": "1.0.15", "@yunti-private/env-impl-rn": "workspace:*", "@yunti-private/env-impl-weapp": "1.0.4", "@yunti-private/env-impl-web": "1.0.18", "@yunti-private/logger-rn": "3.0.3", "@yunti-private/net": "1.0.27", "@yunti-private/net-impl-rn": "workspace:*", "@yunti-private/net-impl-weapp": "1.0.17", "@yunti-private/net-impl-web": "1.0.59", "@yunti-private/net-query-hooks": "0.2.5", "@yunti-private/net-sign-wasm": "0.1.14", "@yunti-private/rn-expo-updates-helper": "0.1.16", "@yunti-private/rn-memory-logger": "1.0.6", "@yunti-private/storage": "1.0.1", "@yunti-private/utils-rn": "1.0.15", "@yunti-private/utils-taro": "1.0.15", "@yunti-private/utils-universal": "1.0.15", "@yunti-private/utils-web": "1.0.15", "@yunti/react-native-chivox": "https://github.com/yuntitech/react-native-chivox#405cbd3857de65d5bcbfd53d238734e3237c5277", "ahooks": "3.7.6", "archiver": "6.0.1", "axios": "1.5.1", "babel-plugin-lodash": "3.3.4", "classnames": "2.3.2", "copy-to-clipboard": "3.3.3", "crypto-js": "4.1.1", "dayjs": "1.11.7", "debug": "4.3.7", "deep-cleaner": "1.2.1", "dotenv": "16.3.1", "event-target-polyfill": "0.0.4", "expo": "53.0.20", "expo-audio": "0.4.8", "expo-blur": "14.1.5", "expo-build-properties": "0.14.8", "expo-camera": "16.1.11", "expo-clipboard": "7.1.5", "expo-constants": "17.1.7", "expo-dev-client": "5.2.4", "expo-device": "7.1.4", "expo-font": "13.3.2", "expo-haptics": "14.1.4", "expo-image": "2.4.0", "expo-image-manipulator": "13.1.7", "expo-image-picker": "16.1.4", "expo-linear-gradient": "14.1.5", "expo-linking": "7.1.7", "expo-localization": "16.1.6", "expo-media-library": "17.1.7", "expo-router": "5.1.4", "expo-screen-orientation": "8.1.7", "expo-splash-screen": "0.30.10", "expo-status-bar": "2.2.3", "expo-system-ui": "5.0.10", "expo-tracking-transparency": "5.2.4", "expo-updates": "0.28.14", "expo-web-browser": "14.2.0", "form-data": "4.0.0", "howler": "2.2.4", "inobounce": "0.2.1", "inversify-props": "2.2.6", "jotai": "2.2.1", "js-base64": "3.7.5", "katex": "0.16.22", "lodash": "4.17.21", "lodash-es": "4.17.21", "lodash-webpack-plugin": "0.11.6", "lottie-miniprogram": "1.0.12", "lottie-react-native": "7.2.2", "mime": "4.0.1", "moment": "2.29.4", "native-wechat": "git+https://git.bookln.cn/github/native-wechat.git#0a8bf80d7b7fd40ea6f5f0dd8c35dcfe2a2f6449", "nativewind": "2.0.11", "query-string": "7.1.1", "react": "19.0.0", "react-dom": "19.0.0", "react-markdown-math": "1.0.2", "react-native": "0.79.5", "react-native-anchor-point": "1.0.6", "react-native-animatable": "1.4.0", "react-native-blob-util": "0.19.4", "react-native-canvas": "0.1.40", "react-native-device-info": "10.12.1", "react-native-drawer-layout": "3.3.0", "react-native-drop-shadow": "1.0.0", "react-native-gesture-handler": "2.24.0", "react-native-iap": "12.16.2", "react-native-image-colors": "2.5.0", "react-native-image-viewing": "0.2.2", "react-native-keyboard-controller": "1.17.5", "react-native-linear-gradient": "2.8.3", "react-native-marked": "6.0.7", "react-native-math-view": "3.9.5", "react-native-modal": "14.0.0-rc.1", "react-native-network-logger": "2.0.0", "react-native-pdf": "6.7.7", "react-native-permissions": "5.4.1", "react-native-popover-view": "6.1.0", "react-native-reanimated": "3.17.5", "react-native-reanimated-table": "0.0.2", "react-native-reanimated-viewer": "1.6.0", "react-native-render-html": "6.3.4", "react-native-root-siblings": "5.0.1", "react-native-root-toast": "3.5.1", "react-native-safe-area-context": "5.4.0", "react-native-screens": "4.11.1", "react-native-select-dropdown": "3.4.0", "react-native-share": "12.0.11", "react-native-skia-shadow": "1.1.0", "react-native-snap-carousel": "3.9.1", "react-native-svg": "15.11.2", "react-native-swiper": "1.6.0", "react-native-table-component": "1.2.2", "react-native-toast-hybrid": "2.6.0", "react-native-url-polyfill": "2.0.0", "react-native-user-agent": "https://github.com/yuntitech/react-native-user-agent.git", "react-native-view-shot": "4.0.3", "react-native-vision-camera": "4.7.0", "react-native-web": "0.20.0", "react-native-webview": "13.15.0", "react-redux": "8.0.5", "react-syntax-highlighter": "15.6.1", "redux": "4.2.1", "reflect-metadata": "0.1.13", "rn-alioss": "0.2.5", "tamagui": "1.129.11", "taro-canvas": "0.0.3", "taro-ui": "https://github.com/yuntitech/package-taro-ui.git", "taro-virtual-list": "1.1.2", "use-debounce": "10.0.4", "uuid": "3.4.0", "wasm-dce": "1.0.2", "wasm-loader": "1.3.0", "weixin-js-sdk": "1.6.3", "weixin-js-sdk-ts": "1.6.1", "yet-another-abortcontroller-polyfill": "0.0.4"}, "peerDependencies": {"@yunti-private/rn-suntone": "0.0.8"}, "resolutions": {"core-js": "2.6.12"}, "pnpm": {"overrides": {"uuid": "3.4.0", "react-refresh": "0.14.0", "tamagui": "1.129.11", "@tamagui/colors": "1.129.11", "@tamagui/compose-refs": "1.129.11", "@tamagui/constants": "1.129.11", "@tamagui/create-theme": "1.129.11", "@tamagui/helpers": "1.129.11", "@tamagui/normalize-css-color": "1.129.11", "@tamagui/shorthands": "1.129.11", "@tamagui/simple-hash": "1.129.11", "@tamagui/theme-builder": "1.129.11", "@tamagui/themes": "1.129.11", "@tamagui/utils": "1.129.11", "@tamagui/cli": "1.129.11", "@tamagui/types": "1.129.11", "@tamagui/use-did-finish-ssr": "1.129.11", "@tamagui/use-theme-name": "1.129.11", "@tamagui/use-force-update": "1.129.11", "@tamagui/web": "1.129.11", "metro": "0.82.4", "metro-cache": "0.82.4", "metro-core": "0.82.4", "metro-runtime": "0.82.4", "metro-resolver": "0.82.4", "metro-babel-transformer": "0.82.4", "metro-source-map": "0.82.4", "@react-navigation/drawer": "7.4.1", "@react-navigation/native": "7.1.10", "@react-navigation/bottom-tabs": "7.3.14", "@react-navigation/native-stack": "7.3.14", "babel-preset-expo": "13.2.0", "expo-file-system": "18.1.11", "expo-modules-autolinking": "2.1.14", "@expo/config-plugins": "10.1.2", "@expo/prebuild-config": "9.0.11", "@expo/metro-config": "0.20.17", "metro-config": "0.82.5", "marked": "5.0.5"}, "patchedDependencies": {"rn-alioss@0.2.5": "patches/<EMAIL>", "react-native-canvas@0.1.40": "patches/<EMAIL>", "@sleiv/react-native-graceful-exit@0.1.0": "patches/@<EMAIL>", "react-native-math-view@3.9.5": "patches/<EMAIL>", "react-native-animatable@1.4.0": "patches/<EMAIL>"}}, "expo": {"install": {"//": ["说明：", "expo:check 命令要求 expo@49.0.13 依赖 react-native-gesture-handler@2.12.0", "但 iOS 只有 react-native-gesture-handler@2.13.4 才能编译过", "所以这里排除掉"], "exclude": ["react-native-gesture-handler"]}}}