import { storage } from '@jgl/utils';
import { atom } from 'jotai';
import { atomWithStorage } from 'jotai/utils';
import { storageKeys } from '../constants/storageKeys';

export const isAgreementAcceptedAtom = atom<boolean | undefined>(undefined);

export type AgreementState =
  | 'loading'
  | 'agreed'
  | 'isAgreeing'
  | 'disagreed'
  | 'isDisagreeing'
  | 'undetermined';

export const agreementStateAtom = atomWithStorage<AgreementState>(
  storageKeys.agreementState,
  'loading',
  {
    setItem: async (key, newValue) => {
      await storage.setItem(
        key,
        newValue === 'disagreed' ? 'undetermined' : newValue,
        { env: false },
      );
    },
    getItem: async (key, initialValue) => {
      const result = await storage.getItem(key, { env: false });
      if (result) {
        return result as AgreementState;
      }
      return 'undetermined';
    },
    removeItem: async (key) => {
      await storage.removeItem(key, { env: false });
    },
  },
);

export const isAcceptingAgreementAtom = atom<boolean | undefined>(undefined);
